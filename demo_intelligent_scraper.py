#!/usr/bin/env python3
"""
智能爬虫系统演示

展示基于自然语言和 OpenAI LLM 的智能爬虫功能
"""

import asyncio
import os
from intelligent_web_scraper import IntelligentWebScraper


async def demo_basic_scraping():
    """演示基本爬取功能"""
    print("🌐 演示1: 基本网页内容提取")
    print("-" * 40)
    
    # 模拟用户输入
    user_inputs = [
        "爬取百度首页的标题和搜索框信息: https://www.baidu.com",
        "提取GitHub首页的导航菜单和标题: https://github.com",
        "获取Python官网的标题和描述: https://www.python.org"
    ]
    
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("⚠️  需要设置 OPENAI_API_KEY 环境变量")
        return
    
    scraper = IntelligentWebScraper(api_key)
    
    for i, user_input in enumerate(user_inputs, 1):
        print(f"\n📝 示例 {i}: {user_input}")
        
        try:
            result = await scraper.scrape_from_natural_language(user_input)
            
            if result.success:
                print("✅ 爬取成功!")
                print(f"🔗 URL: {result.url}")
                
                if result.extracted_data:
                    extracted = result.extracted_data.get('extracted_data', {})
                    summary = result.extracted_data.get('summary', '无摘要')
                    confidence = result.extracted_data.get('confidence', 0)
                    
                    print(f"📊 提取摘要: {summary}")
                    print(f"🎯 置信度: {confidence:.1%}" if confidence else "")
                    
                    # 显示部分提取数据
                    if extracted:
                        print("📋 提取的内容:")
                        for key, value in list(extracted.items())[:3]:  # 只显示前3项
                            if isinstance(value, str) and len(value) > 100:
                                value = value[:100] + "..."
                            print(f"  • {key}: {value}")
                
            else:
                print(f"❌ 爬取失败: {result.error_message}")
                
        except Exception as e:
            print(f"❌ 发生错误: {e}")
        
        # 添加延迟
        if i < len(user_inputs):
            await asyncio.sleep(2)


async def demo_intent_analysis():
    """演示意图分析功能"""
    print("\n🧠 演示2: 自然语言意图分析")
    print("-" * 40)
    
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("⚠️  需要设置 OPENAI_API_KEY 环境变量")
        return
    
    scraper = IntelligentWebScraper(api_key)
    
    # 测试不同类型的输入
    test_inputs = [
        "我想爬取淘宝商品页面的价格和评论",
        "帮我抓取这个新闻网站的标题: https://news.example.com",
        "爬取会员页面内容，账号是**************，密码是123456，网址是https://member.site.com",
        "提取这个表格的所有数据: https://data.gov.cn/table"
    ]
    
    for i, user_input in enumerate(test_inputs, 1):
        print(f"\n📝 输入 {i}: {user_input}")
        
        try:
            intent = scraper.intent_analyzer.analyze_intent(user_input)
            
            print("🎯 分析结果:")
            print(f"  URL: {intent.url or '未提供'}")
            print(f"  内容要求: {', '.join(intent.content_requirements) if intent.content_requirements else '未指定'}")
            print(f"  需要登录: {'是' if intent.needs_login else '否'}")
            
            if intent.username:
                print(f"  用户名: {intent.username}")
            if intent.password:
                print(f"  密码: {'*' * len(intent.password)}")
            if intent.additional_info:
                print(f"  附加信息: {intent.additional_info}")
                
        except Exception as e:
            print(f"❌ 分析失败: {e}")


def demo_configuration():
    """演示配置选项"""
    print("\n⚙️  演示3: 配置选项")
    print("-" * 40)
    
    print("🔧 支持的配置选项:")
    print("  • timeout: 请求超时时间")
    print("  • wait_for_js: JavaScript 渲染等待时间")
    print("  • retries: 重试次数")
    print("  • user_agent: 自定义 User-Agent")
    print("  • headers: 自定义请求头")
    
    print("\n📝 配置示例:")
    config_example = '''
from langchain_html_processor import LoaderConfig
from intelligent_web_scraper import IntelligentWebScraper

# 自定义配置
config = LoaderConfig(
    timeout=120,
    wait_for_js=5.0,
    retries=3,
    user_agent="Custom Bot 1.0"
)

scraper = IntelligentWebScraper("your-api-key")
scraper.loader_config = config
'''
    print(config_example)


def demo_batch_processing():
    """演示批量处理"""
    print("\n📦 演示4: 批量处理")
    print("-" * 40)
    
    print("🚀 批量处理功能:")
    print("  • 支持多个任务并行处理")
    print("  • 自动错误处理和重试")
    print("  • 结果统计和报告")
    print("  • 支持从文件加载任务")
    
    print("\n📝 使用方法:")
    print("  python batch_scraper.py")
    print("  python batch_scraper.py --create-sample")
    
    print("\n📋 任务文件格式 (JSON):")
    example_tasks = '''
{
  "description": "示例爬取任务",
  "tasks": [
    "爬取百度首页的标题: https://www.baidu.com",
    "提取GitHub首页信息: https://github.com",
    "获取Python官网内容: https://www.python.org"
  ]
}
'''
    print(example_tasks)


def show_architecture():
    """显示系统架构"""
    print("\n🏗️  系统架构")
    print("-" * 40)
    
    print("📊 核心组件:")
    print("  🤖 IntelligentWebScraper - 主控制器")
    print("  🧠 IntentAnalyzer - 意图分析器")
    print("  📄 ContentExtractor - 内容提取器")
    print("  🔐 AuthenticatedHTMLLoader - 认证加载器")
    print("  🌐 SmartHTMLLoader - 智能加载器")
    
    print("\n🔄 工作流程:")
    print("  1. 用户输入自然语言描述")
    print("  2. AI 分析意图，提取关键信息")
    print("  3. 选择合适的加载策略")
    print("  4. 执行网页爬取")
    print("  5. AI 解析和提取内容")
    print("  6. 格式化输出结果")
    
    print("\n🎯 技术特点:")
    print("  ✨ 自然语言理解")
    print("  🔐 智能登录检测")
    print("  🧠 AI 驱动提取")
    print("  🚀 高度通用化")
    print("  📊 结构化输出")


async def main():
    """主演示函数"""
    print("🤖 智能网页爬虫系统演示")
    print("=" * 60)
    
    print("✨ 基于自然语言输入和 OpenAI LLM 的智能爬虫")
    print("🔧 基于通用的 langchain_html_processor 包")
    print("🎯 支持自动登录、智能内容提取、批量处理")
    
    # 检查 API Key
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("\n⚠️  注意: 需要设置 OPENAI_API_KEY 环境变量才能运行完整演示")
        print("💡 可以运行以下命令设置:")
        print("   export OPENAI_API_KEY='your-api-key-here'")
        print("\n📖 当前将显示功能介绍和配置示例")
    
    # 显示系统架构
    show_architecture()
    
    # 演示配置选项
    demo_configuration()
    
    # 演示批量处理
    demo_batch_processing()
    
    if api_key:
        # 演示意图分析
        await demo_intent_analysis()
        
        # 演示基本爬取（如果有 API Key）
        print("\n🔄 开始实际爬取演示...")
        await demo_basic_scraping()
    
    print("\n🎉 演示完成!")
    print("=" * 60)
    
    print("🚀 快速开始:")
    print("  python interactive_scraper.py  # 交互式界面")
    print("  python batch_scraper.py       # 批量处理")
    
    print("\n📚 更多信息:")
    print("  README_intelligent_scraper.md # 详细文档")
    print("  intelligent_web_scraper.py    # 核心实现")
    
    print("\n💡 使用提示:")
    print("  • 用自然语言描述你的爬取需求")
    print("  • 系统会自动理解并执行")
    print("  • 支持登录、内容提取、批量处理")
    print("  • 基于强大的 langchain_html_processor")


if __name__ == "__main__":
    asyncio.run(main())
