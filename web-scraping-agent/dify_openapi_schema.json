{"openapi": "3.1.0", "info": {"title": "Web Scraping Agent", "description": "Intelligent web scraping agent that supports natural language input, automatic login detection, and smart content extraction", "version": "v1.0.0"}, "servers": [{"url": "http://localhost:8000", "description": "Local development server"}, {"url": "https://your-domain.com", "description": "Production server"}], "paths": {"/tools/web-scraping": {"post": {"summary": "Intelligent Web Scraping", "description": "Extract content from web pages using natural language descriptions. Supports automatic login and AI-powered content extraction.", "operationId": "WebScraping", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScrapingRequest"}}}}, "responses": {"200": {"description": "Successful scraping response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScrapingResponse"}}}}, "400": {"description": "Bad request - invalid parameters"}, "500": {"description": "Internal server error"}}, "deprecated": false}}, "/health": {"get": {"summary": "Health Check", "description": "Check the health status of the web scraping agent", "operationId": "HealthCheck", "responses": {"200": {"description": "Service is healthy", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthResponse"}}}}}, "deprecated": false}}}, "components": {"schemas": {"ScrapingRequest": {"type": "object", "required": ["description"], "properties": {"description": {"type": "string", "description": "Natural language description of what you want to scrape from the website", "example": "Extract product name, price and reviews from this e-commerce page"}, "url": {"type": "string", "format": "uri", "description": "The URL of the webpage to scrape (optional if included in description)", "example": "https://shop.example.com/product/123"}, "username": {"type": "string", "description": "Username for login (if the website requires authentication)", "example": "<EMAIL>"}, "password": {"type": "string", "description": "Password for login (if the website requires authentication)", "example": "mypassword"}}}, "ScrapingResponse": {"type": "object", "properties": {"status": {"type": "string", "enum": ["success", "error"], "description": "Status of the scraping operation"}, "data": {"type": "object", "description": "Extracted content data", "additionalProperties": true}, "summary": {"type": "string", "description": "Summary of the scraping result"}, "metadata": {"type": "object", "properties": {"url": {"type": "string", "description": "The URL that was scraped"}, "confidence": {"type": "number", "minimum": 0, "maximum": 1, "description": "Confidence score of the extraction (0-1)"}, "content_length": {"type": "integer", "description": "Length of the original content"}, "authenticated": {"type": "boolean", "description": "Whether authentication was used"}}}}}, "HealthResponse": {"type": "object", "properties": {"status": {"type": "string", "enum": ["healthy", "unhealthy"], "description": "Health status of the service"}, "timestamp": {"type": "string", "format": "date-time", "description": "Timestamp of the health check"}, "version": {"type": "string", "description": "Version of the service"}, "components": {"type": "object", "description": "Status of individual components", "additionalProperties": {"type": "string"}}}}}, "securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "description": "Optional bearer token for API access control"}}}, "security": [{"BearerAuth": []}, {}]}