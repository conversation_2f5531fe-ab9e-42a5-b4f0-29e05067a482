# HTML压缩优化方案

## 问题描述

网页数据提取中GPT prompt过大的问题：
- HTML内容经过压缩后仍可能达到12000字符
- System prompt本身很长，特别是多项目模式下
- 总请求大小经常超过15000字符，触发进一步缩减
- 影响提取质量和API调用效率

## 解决方案

### 1. 增强的HTML压缩管道

采用多步骤压缩策略，集成多种压缩技术：

#### 步骤1: 基础清理
- 移除script、style、注释标签
- 移除无用属性和空白字符
- 使用现有的`_clean_html_content()`方法

#### 步骤2: htmlmin压缩
- 使用专业的HTML压缩库`htmlmin`
- 移除HTML注释和多余空白
- 压缩HTML属性和标签
- 预期压缩率：10-20%

#### 步骤3: BeautifulSoup智能提取
- 使用`BeautifulSoup`进行精确的HTML解析
- 智能识别商品相关容器
- 移除导航、页脚、广告等无关内容
- 只保留包含商品信息的核心元素
- 预期压缩率：30-50%

#### 步骤4: html2text备选方案
- 当HTML仍然过大时（>8KB），使用`html2text`转换
- 将HTML转换为结构化文本，大幅减少大小
- 保留链接和图片信息
- 预期压缩率：50-70%

### 2. 优化的System Prompt

#### 多项目模式优化
```
你是专业网页数据提取器。从HTML中提取所有商品的{requirements}。

任务：提取页面所有商品信息
URL：{url}

返回JSON格式：
{
    "extracted_data": {
        "items": [
            {"field1": "value1", "field2": "value2"}
        ]
    },
    "summary": "找到X个商品",
    "confidence": 0.8,
    "notes": "说明"
}

要求：
1. 提取所有商品，放入items数组
2. 缺失字段设为null
3. 返回有效JSON，无注释
4. 在summary中说明商品数量
```

#### 单项目模式优化
```
你是智能内容提取器。从HTML中提取{requirements}。

URL：{url}

返回JSON格式：
{
    "extracted_data": {"field1": "value1", "field2": "value2"},
    "summary": "提取结果总结",
    "confidence": 0.8,
    "notes": "说明"
}

要求：
1. 使用指定字段名
2. 保留完整格式（价格、链接等）
3. 缺失内容设为null
4. 返回有效JSON，无注释
```

## 技术实现

### 新增依赖
```bash
pip install htmlmin==0.1.12
pip install html2text==2020.1.16
```

### 核心方法

1. `_htmlmin_compress()` - htmlmin基础压缩
2. `_beautifulsoup_extract_smart()` - 智能商品提取
3. `_html2text_fallback()` - 文本转换备选方案
4. `_optimize_system_prompt()` - 优化prompt生成

### 集成方式

修改`_compress_html_content()`方法，集成新的压缩管道：
```python
def _compress_html_content(self, html_content: str, compression_level: str = "aggressive") -> str:
    # 步骤1: 基础清理
    html_content = self._clean_html_content(html_content)
    
    # 步骤2: htmlmin压缩
    html_content = self._htmlmin_compress(html_content)
    
    # 步骤3: BeautifulSoup智能提取
    html_content = self._beautifulsoup_extract_smart(html_content)
    
    # 步骤4: 检查大小，必要时使用html2text
    if len(html_content) > 8000:
        html_content = self._html2text_fallback(html_content)
    
    return html_content
```

## 预期效果

### 压缩效果
- **HTML内容大小减少**: 50-70%
- **System prompt大小减少**: 20-30%
- **总体token使用量减少**: 40-60%

### 性能提升
- 减少API调用成本
- 提高响应速度
- 降低token限制触发频率
- 保持或提高提取准确性

## 安装和测试

### 1. 安装依赖
```bash
python install_dependencies.py
```

### 2. 测试压缩效果
```bash
python test_compression.py
```

### 3. 验证功能
运行现有的网页抓取测试，验证：
- 压缩后的HTML仍能正确提取商品信息
- API请求大小显著减少
- 提取质量保持稳定

## 兼容性

- 向后兼容现有代码
- 优雅降级：如果新依赖未安装，自动回退到原有方法
- 保持现有API接口不变

## 监控和调优

### 日志监控
- 记录每个压缩步骤的效果
- 监控最终请求大小
- 跟踪压缩率和提取质量

### 参数调优
- 可调整html2text触发阈值（默认8KB）
- 可配置BeautifulSoup提取策略
- 可自定义htmlmin压缩选项

## 总结

这个优化方案通过多层次的压缩技术，在保持提取质量的同时，显著减少了token使用量。采用渐进式压缩策略，确保在各种情况下都能获得最佳的压缩效果。
