#!/usr/bin/env python3
"""
快速测试脚本

用于快速验证空结果问题的修复
"""

import os
import sys
import json
import asyncio
from intelligent_web_scraper import IntelligentWebScraper


async def quick_test():
    """快速测试函数"""
    
    print("🚀 快速测试开始...")
    
    # 检查API密钥
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ 请设置环境变量: export OPENAI_API_KEY='your-key'")
        return False
    
    # 创建爬虫实例
    scraper = IntelligentWebScraper(api_key)
    
    # 测试用例（来自用户的实际案例）
    user_input = """请帮我爬取这个页面的所有商品的数据
description：爬取这个商品页面的名称、价格、图片，upc等信息
URL: https://member.restaurantdepot.com/products?id=5&sort=saleranking&it=product&category=1%7cCandy%20%26%20Snacks%2c1%7cDry%20Groceries%2c1%7cJanitorial%20Supplies%2c1%7cRetail%20Groceries%20%28Food%29%2c1%7cRetail%20Groceries%20%28Non-Food%29
username：<EMAIL>
password：Jetsons823
content_requirements：title,image, item Number, upc, BIN, price"""
    
    print("📝 测试输入:")
    print(user_input[:200] + "..." if len(user_input) > 200 else user_input)
    
    try:
        # 执行爬取
        print("\n🔄 执行爬取...")
        result = await scraper.scrape_from_natural_language(user_input)
        
        # 分析结果
        print(f"\n📊 结果分析:")
        print(f"✅ 成功: {result.success}")
        
        if result.success:
            extracted_data = result.extracted_data
            
            # 检查是否有商品数据
            if "extracted_data" in extracted_data and "items" in extracted_data["extracted_data"]:
                items = extracted_data["extracted_data"]["items"]
                print(f"🛍️  商品数量: {len(items)}")
                
                if items:
                    print("✅ 成功提取商品！")
                    # 显示第一个商品作为示例
                    first_item = items[0]
                    print("📦 第一个商品示例:")
                    for key, value in first_item.items():
                        print(f"   {key}: {value}")
                    
                    return True
                else:
                    print("⚠️  商品列表为空")
                    
                    # 显示调试信息
                    if "debug_info" in extracted_data:
                        debug_info = extracted_data["debug_info"]
                        print("\n🔍 调试信息:")
                        
                        html_analysis = debug_info.get("html_analysis", {})
                        print(f"   检测到产品: {html_analysis.get('has_products', False)}")
                        print(f"   产品指标数量: {len(html_analysis.get('product_indicators', []))}")
                        
                        possible_issues = debug_info.get("possible_issues", [])
                        print(f"   可能的问题: {possible_issues}")
                        
                        print(f"   内容长度: {debug_info.get('total_content_length', 0)}")
                        print(f"   使用长度: {debug_info.get('content_length_used', 0)}")
                    
                    return False
            else:
                print("❌ 未找到预期的数据结构")
                print(f"实际数据: {json.dumps(extracted_data, indent=2, ensure_ascii=False)[:500]}...")
                return False
        else:
            print(f"❌ 爬取失败: {result.error_message}")
            return False
            
    except Exception as e:
        print(f"💥 异常: {e}")
        import traceback
        traceback.print_exc()
        return False


def show_recommendations():
    """显示建议"""
    
    print("\n" + "=" * 60)
    print("💡 改进建议")
    print("=" * 60)
    
    print("如果测试失败，可以尝试：")
    print("1. 🔍 启用HTML调试: export DEBUG_HTML=true")
    print("2. 📋 查看详细日志输出")
    print("3. 🌐 手动验证网站是否可访问")
    print("4. 🔑 确认登录凭据是否正确")
    print("5. ⏱️  增加页面等待时间")
    
    print("\n调试命令:")
    print("python debug_empty_results.py  # 运行详细调试")
    print("python test_restaurant_depot.py  # 运行完整测试")


async def main():
    """主函数"""
    
    print("=" * 60)
    print("🧪 智能网页爬虫 - 空结果问题快速测试")
    print("=" * 60)
    
    success = await quick_test()
    
    if success:
        print("\n🎉 测试成功！问题已解决。")
    else:
        print("\n🔧 测试失败，需要进一步调试。")
        show_recommendations()
    
    print("\n" + "=" * 60)
    print("✅ 快速测试完成")
    print("=" * 60)


if __name__ == "__main__":
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        sys.exit(1)
    
    # 运行测试
    asyncio.run(main())
