"""
Document class for HTML processing
"""

from typing import Dict, Any, Optional


class Document:
    """
    Simple document class compatible with LangChain Document
    """
    
    def __init__(self, page_content: str, metadata: Optional[Dict[str, Any]] = None):
        """
        Initialize document
        
        Args:
            page_content: The main content of the document
            metadata: Optional metadata dictionary
        """
        self.page_content = page_content
        self.metadata = metadata or {}
    
    def __repr__(self) -> str:
        return f"Document(page_content='{self.page_content[:50]}...', metadata={self.metadata})"
    
    def __str__(self) -> str:
        return self.page_content
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert document to dictionary"""
        return {
            "page_content": self.page_content,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Document":
        """Create document from dictionary"""
        return cls(
            page_content=data.get("page_content", ""),
            metadata=data.get("metadata", {})
        )
