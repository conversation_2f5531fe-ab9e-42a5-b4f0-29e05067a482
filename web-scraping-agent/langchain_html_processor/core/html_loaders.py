"""
Simplified HTML Loaders for Web Scraping Agent

Embedded version with core functionality needed for the agent.
"""

import asyncio
import logging
import re
from typing import List, Dict, Optional, Union, Any
from abc import ABC, abstractmethod

import requests
from bs4 import BeautifulSoup

try:
    import aiohttp
except ImportError:
    aiohttp = None

try:
    from playwright.async_api import async_playwright
except ImportError:
    async_playwright = None

from .document import Document
from .config import LoaderConfig

# Setup logging
logger = logging.getLogger(__name__)


class BaseHTMLLoader(ABC):
    """Base class for HTML loaders"""
    
    def __init__(self, config: Optional[LoaderConfig] = None):
        self.config = config or LoaderConfig()
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    def load(self, source: Union[str, List[str]]) -> List[Document]:
        """Load HTML content and return documents"""
        pass
    
    def _analyze_html_content(self, html_content: str) -> Dict[str, Any]:
        """分析HTML内容，检测产品、结构等信息"""
        analysis = {
            "has_products": False,
            "product_indicators": [],
            "structure_info": {},
            "content_quality": {},
            "page_type": "unknown"
        }

        # 常见的产品相关HTML模式
        product_patterns = [
            (r'class="[^"]*product[^"]*"', "product class"),
            (r'class="[^"]*item[^"]*"', "item class"),
            (r'class="[^"]*card[^"]*"', "card class"),
            (r'data-product', "product data attribute"),
            (r'data-item', "item data attribute"),
            (r'\$\d+\.\d+', "price pattern"),
            (r'price["\s]*[:=]', "price field"),
            (r'title["\s]*[:=]', "title field"),
            (r'<img[^>]*src', "image tags"),
            (r'upc["\s]*[:=]', "UPC field"),
            (r'sku["\s]*[:=]', "SKU field"),
            (r'item.?number', "item number field"),
            (r'bin["\s]*[:=]', "BIN field"),
        ]

        # 检测产品相关模式
        for pattern, description in product_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            if matches:
                analysis["product_indicators"].append({
                    "pattern": description,
                    "count": len(matches),
                    "examples": matches[:2] if len(matches) <= 10 else [matches[0]]  # 减少示例数量
                })
                analysis["has_products"] = True

        # 分析HTML结构
        analysis["structure_info"] = {
            "total_length": len(html_content),
            "div_count": len(re.findall(r'<div', html_content, re.IGNORECASE)),
            "class_count": len(re.findall(r'class=', html_content, re.IGNORECASE)),
            "img_count": len(re.findall(r'<img', html_content, re.IGNORECASE)),
            "link_count": len(re.findall(r'<a', html_content, re.IGNORECASE)),
            "form_count": len(re.findall(r'<form', html_content, re.IGNORECASE)),
            "table_count": len(re.findall(r'<table', html_content, re.IGNORECASE))
        }

        # 检测页面类型
        content_lower = html_content.lower()
        if any(indicator in content_lower for indicator in ["login", "sign in", "password", "username"]):
            analysis["page_type"] = "login"
        elif any(indicator in content_lower for indicator in ["error", "not found", "access denied", "unauthorized"]):
            analysis["page_type"] = "error"
        elif analysis["has_products"]:
            analysis["page_type"] = "product_listing"
        elif any(indicator in content_lower for indicator in ["home", "welcome", "dashboard"]):
            analysis["page_type"] = "homepage"

        # 内容质量评估
        analysis["content_quality"] = {
            "has_meaningful_content": len(html_content.strip()) > 1000,
            "has_navigation": "nav" in content_lower or "menu" in content_lower,
            "has_forms": analysis["structure_info"]["form_count"] > 0,
            "complexity_score": min(10, analysis["structure_info"]["div_count"] / 10)  # 0-10分
        }

        return analysis

    def _extract_content(self, html_content: str) -> str:
        """Extract text content from HTML"""
        soup = BeautifulSoup(html_content, 'html.parser')

        # Remove script and style elements
        for script in soup(["script", "style"]):
            script.decompose()

        # Get text content
        text = soup.get_text()

        # Clean up whitespace
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text = ' '.join(chunk for chunk in chunks if chunk)

        return text
    
    def _create_document(self, content: str, metadata: Dict[str, Any], html_content: str = None) -> Document:
        """Create a document with content and metadata, including HTML analysis"""
        # 如果提供了HTML内容，进行分析
        if html_content:
            html_analysis = self._analyze_html_content(html_content)
            metadata['html_analysis'] = html_analysis

            # 记录分析结果
            self.logger.info(f"HTML分析完成: 页面类型={html_analysis['page_type']}, "
                           f"检测到产品={html_analysis['has_products']}, "
                           f"产品指标数量={len(html_analysis['product_indicators'])}")

            # 如果没有检测到产品，记录警告
            if not html_analysis['has_products'] and html_analysis['page_type'] not in ['login', 'error']:
                self.logger.warning("⚠️  未在HTML中检测到产品相关内容")

        return Document(page_content=content, metadata=metadata)


class SmartHTMLLoader(BaseHTMLLoader):
    """Smart HTML loader with automatic content extraction"""
    
    def load(self, url: str) -> List[Document]:
        """Load HTML from URL"""
        try:
            response = requests.get(
                url,
                headers=self.config.headers,
                timeout=self.config.timeout,
                verify=self.config.verify_ssl
            )
            response.raise_for_status()

            # Extract content
            content = self._extract_content(response.text)

            metadata = {
                'source': url,
                'title': self._extract_title(response.text),
                'status_code': response.status_code,
                'content_type': response.headers.get('content-type', ''),
                'loader_type': 'smart',
                'raw_html_length': len(response.text)
            }

            # 创建文档时包含HTML分析
            return [self._create_document(content, metadata, response.text)]

        except Exception as e:
            self.logger.error(f"Failed to load {url}: {e}")
            return []
    
    def _extract_title(self, html_content: str) -> str:
        """Extract title from HTML"""
        soup = BeautifulSoup(html_content, 'html.parser')
        title_tag = soup.find('title')
        return title_tag.get_text().strip() if title_tag else ''


class AuthenticatedHTMLLoader(BaseHTMLLoader):
    """HTML loader with authentication support"""
    
    def __init__(self, config: Optional[LoaderConfig] = None):
        super().__init__(config)
        self.login_selectors = {
            'username_field': ['input[name="email"]', 'input[name="username"]', 'input[type="email"]', '#email', '#username'],
            'password_field': ['input[name="password"]', 'input[type="password"]', '#password'],
            'login_button': ['button[type="submit"]', 'input[type="submit"]', '.login-button', '#login-button'],
            'login_form': ['form', '.login-form', '#login-form']
        }

    def load(self, source: str) -> List[Document]:
        """基础 load 方法实现（用于兼容性）"""
        # 这个方法主要用于满足抽象基类要求
        # 实际使用时应该调用 load_with_auth
        self.logger.warning("使用 load_with_auth 方法进行认证加载")
        return []

    async def load_with_auth(self, target_url: str, username: str, password: str,
                           login_url: str, custom_selectors: Optional[Dict[str, List[str]]] = None) -> List[Document]:
        """Load content from authenticated URL"""
        if async_playwright is None:
            raise ImportError("Playwright is required for authenticated loading. Install with: pip install playwright")

        async with async_playwright() as p:
            browser = await p.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )

            try:
                # Check if this is Restaurant Depot and use specialized login
                if "restaurantdepot.com" in login_url.lower():
                    self.logger.info("Using Restaurant Depot specialized login")
                    login_success = await self.login_restaurant_depot(browser, username, password)
                else:
                    # Use general login method
                    login_success = await self.login(browser, username, password, login_url, custom_selectors)
                
                if not login_success:
                    self.logger.error("Login failed, cannot proceed with content loading")
                    return []
                
                # Use the authenticated context to load target content
                page = self._auth_page
                
                # Navigate to target URL
                await page.goto(target_url, timeout=self.config.timeout * 1000)
                await page.wait_for_timeout(self.config.wait_for_js * 1000)
                
                # Get content
                raw_html_content = await page.content()
                title = await page.title()

                # Extract text content
                extracted_content = self._extract_content(raw_html_content)

                metadata = {
                    'source': target_url,
                    'title': title,
                    'type': 'authenticated_url',
                    'loader_type': 'authenticated',
                    'login_url': login_url,
                    'authenticated': True,
                    'raw_html_length': len(raw_html_content)
                }

                # 创建文档时包含HTML分析
                return [self._create_document(extracted_content, metadata, raw_html_content)]
                
            except Exception as e:
                self.logger.error(f"Failed to load authenticated content from {target_url}: {e}")
                return []
            finally:
                if hasattr(self, '_auth_context'):
                    await self._auth_context.close()
                await browser.close()
    
    async def login_restaurant_depot(self, browser, username: str, password: str) -> bool:
        """Specialized login method for Restaurant Depot"""
        try:
            context = await browser.new_context(
                user_agent=self.config.user_agent,
                extra_http_headers={
                    **self.config.headers,
                    "Referer": "https://member.restaurantdepot.com/",
                    "Origin": "https://member.restaurantdepot.com"
                },
                viewport={'width': 1920, 'height': 1080},
                locale='en-US',
                timezone_id='America/New_York'
            )
            
            page = await context.new_page()
            
            # Step 1: Get the login page and extract form_key
            self.logger.info("Getting Restaurant Depot login page...")
            await page.goto("https://member.restaurantdepot.com/", timeout=self.config.timeout * 1000)
            await page.wait_for_timeout(3000)
            
            # Extract form_key
            form_key = None
            try:
                # Try to find form_key element
                form_key_elements = await page.query_selector_all('input[name="form_key"]')
                if form_key_elements:
                    form_key = await form_key_elements[0].get_attribute('value')
                    self.logger.info(f"Found form_key: {form_key[:20]}...")
                else:
                    # Try alternative method - get from page content
                    page_content = await page.content()
                    import re
                    form_key_match = re.search(r'name="form_key"[^>]*value="([^"]+)"', page_content)
                    if form_key_match:
                        form_key = form_key_match.group(1)
                        self.logger.info(f"Found form_key via regex: {form_key[:20]}...")
                    else:
                        self.logger.error("Could not find form_key in page")
                        await context.close()
                        return False
            except Exception as e:
                self.logger.error(f"Error extracting form_key: {e}")
                await context.close()
                return False
            
            if not form_key:
                self.logger.error("form_key is empty")
                await context.close()
                return False
            
            # Step 2: Perform login via POST request
            self.logger.info("Performing Restaurant Depot login...")
            
            # Use page.evaluate to perform the POST request
            login_script = f"""
            async () => {{
                const formData = new FormData();
                formData.append('form_key', '{form_key}');
                formData.append('login[username]', '{username}');
                formData.append('login[password]', '{password}');
                
                const response = await fetch('https://member.restaurantdepot.com/customer/account/loginPost/', {{
                    method: 'POST',
                    body: formData,
                    headers: {{
                        'Referer': 'https://member.restaurantdepot.com/',
                        'Origin': 'https://member.restaurantdepot.com'
                    }}
                }});
                
                return {{
                    status: response.status,
                    url: response.url,
                    text: await response.text()
                }};
            }}
            """
            
            login_result = await page.evaluate(login_script)
            self.logger.info(f"Login POST response: status={login_result['status']}, url={login_result['url']}")
            
            # Check if login was successful
            if "/products" in login_result.get('text', '') or login_result['status'] == 200:
                self.logger.info("Login appears successful")
                
                # Step 3: Set user location (California, region 19)
                self.logger.info("Setting user location...")
                location_script = f"""
                async () => {{
                    const response = await fetch('https://member.restaurantdepot.com/location/save/userlocation?form_key={form_key}&warehouse_state=CA&region_id=19', {{
                        method: 'POST',
                        headers: {{
                            'Referer': 'https://member.restaurantdepot.com/',
                            'Origin': 'https://member.restaurantdepot.com'
                        }}
                    }});
                    return {{
                        status: response.status,
                        text: await response.text()
                    }};
                }}
                """
                
                location_result = await page.evaluate(location_script)
                self.logger.info(f"Location setup: status={location_result['status']}")
                
                # Store the context for reuse
                self._auth_context = context
                self._auth_page = page
                
                return True
            else:
                self.logger.error("Login failed - products page not found in response")
                await context.close()
                return False
                
        except Exception as e:
            self.logger.error(f"Restaurant Depot login failed with error: {e}")
            if 'context' in locals():
                await context.close()
            return False
    
    async def login(self, browser, username: str, password: str, login_url: str, 
                   custom_selectors: Optional[Dict[str, List[str]]] = None) -> bool:
        """General login method for other websites"""
        # This is a simplified version - you can expand this for other sites
        self.logger.warning("General login method not fully implemented")
        return False
