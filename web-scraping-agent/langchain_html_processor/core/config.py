"""
Configuration classes for HTML processing
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass


@dataclass
class LoaderConfig:
    """Configuration for HTML loaders"""
    
    # Request settings
    timeout: float = 30.0
    retries: int = 3
    requests_per_second: float = 2.0
    
    # Browser settings
    wait_for_js: float = 2.0
    user_agent: str = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    headers: Dict[str, str] = None
    
    # Proxy settings
    proxies: Optional[List[str]] = None
    
    # Content processing
    encoding: Optional[str] = None
    verify_ssl: bool = True
    
    def __post_init__(self):
        """Initialize default values"""
        if self.headers is None:
            self.headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary"""
        return {
            'timeout': self.timeout,
            'retries': self.retries,
            'requests_per_second': self.requests_per_second,
            'wait_for_js': self.wait_for_js,
            'user_agent': self.user_agent,
            'headers': self.headers,
            'proxies': self.proxies,
            'encoding': self.encoding,
            'verify_ssl': self.verify_ssl
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "LoaderConfig":
        """Create config from dictionary"""
        return cls(**data)
