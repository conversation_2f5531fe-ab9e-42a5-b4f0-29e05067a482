# JSON解析错误修复

## 问题描述

用户在Dify中使用Web Scraping Agent时遇到JSON解析失败的错误：

```
2025-07-22 11:15:10,244 - intelligent_web_scraper - ERROR - Failed to parse AI response as JSON: {
    "url": "https://member.restaurantdepot.com/products?id=5&sort=saleranking&it=product&category=1%7cCandy%20%26%20Snacks%2c1%7cDry%20Groceries%2c1%7cJanitorial%20Supplies%2c1%7cRetail%20Groceries%20%28Food%29%2c1%7cRetail%20Groceries%20%28Non-Food%29",
    "content_requirements": ["名称", "价格", "图片", "upc", "item Number", "BIN"],
    "needs_login": true,
    "username": "<EMAIL>",
    "password": "Jetsons823",
    "login_url": "https://member.restaurantdepot.com/login",  // 假设的登录页面URL
    "additional_info": {
        "其他": "请确认登录页面URL是否正确，或提供具体的登录页面URL。"
    }
}
```

## 问题根因

AI返回的JSON中包含了注释（`// 假设的登录页面URL`），而标准JSON格式不支持注释，导致`json.loads()`解析失败。

## 解决方案

### 1. 新增JSON清理功能

在`IntentAnalyzer`和`ContentExtractor`类中都添加了`_clean_json_response()`方法：

```python
def _clean_json_response(self, json_text: str) -> str:
    """清理AI返回的JSON中的注释和多余内容"""
    import re
    
    # 更精确的注释移除：只移除不在字符串内的注释
    lines = json_text.split('\n')
    cleaned_lines = []
    
    for line in lines:
        # 检查是否在字符串内
        in_string = False
        escape_next = False
        comment_start = -1
        
        for i, char in enumerate(line):
            if escape_next:
                escape_next = False
                continue
                
            if char == '\\':
                escape_next = True
                continue
                
            if char == '"' and not escape_next:
                in_string = not in_string
                continue
                
            # 只在字符串外查找注释
            if not in_string:
                if i < len(line) - 1 and line[i:i+2] == '//':
                    comment_start = i
                    break
        
        # 移除注释部分
        if comment_start >= 0:
            line = line[:comment_start].rstrip()
        
        cleaned_lines.append(line)
    
    # 重新组合
    json_text = '\n'.join(cleaned_lines)
    
    # 移除多行注释 /* ... */
    json_text = re.sub(r'/\*.*?\*/', '', json_text, flags=re.DOTALL)
    
    # 清理多余的空白字符
    json_text = re.sub(r'\n\s*\n', '\n', json_text)
    
    return json_text.strip()
```

### 2. 更新JSON解析逻辑

在解析JSON之前先清理注释：

```python
# IntentAnalyzer中
try:
    # 清理JSON中的注释
    cleaned_json = self._clean_json_response(result_text)
    result_json = json.loads(cleaned_json)
    # ... 处理逻辑
except json.JSONDecodeError:
    # 错误处理
```

```python
# ContentExtractor中
try:
    # 清理JSON中的注释
    cleaned_result = self._clean_json_response(result_text)
    result = json.loads(cleaned_result)
    # ... 处理逻辑
except json.JSONDecodeError:
    # 错误处理
```

### 3. 更新AI提示

在AI提示中明确要求不要包含注释：

```python
请以JSON格式返回结果（注意：不要在JSON中包含任何注释）：
{
    "url": "目标网址",
    "content_requirements": ["需要爬取的内容1", "内容2"],
    "needs_login": true,
    "username": "用户名（如果提供）",
    "password": "密码（如果提供）",
    "login_url": "登录页面URL（如果不同于主URL）",
    "additional_info": {
        "其他": "相关信息"
    }
}

重要：返回的必须是纯净的JSON格式，不要包含任何注释（//或/**/）。
```

## 修复效果

### 修复前
- ❌ AI返回包含注释的JSON
- ❌ `json.loads()`解析失败
- ❌ 用户看到"AI响应解析失败"错误
- ❌ 爬取功能无法正常工作

### 修复后
- ✅ 自动清理JSON中的注释
- ✅ 保留URL中的`//`（不误删）
- ✅ 成功解析JSON
- ✅ 爬取功能正常工作

## 测试验证

### 测试用例1：单行注释
```json
// 修复前：解析失败
{
    "url": "https://example.com",
    "content_requirements": ["title", "price"], // 这是注释
    "needs_login": true
}

// 修复后：解析成功
{
    "url": "https://example.com",
    "content_requirements": ["title", "price"],
    "needs_login": true
}
```

### 测试用例2：复杂注释组合
```json
// 修复前：解析失败
{
    "url": "https://member.restaurantdepot.com/login",  // 假设的登录页面URL
    "content_requirements": ["名称", "价格", "图片", "upc"],
    "needs_login": true
}

// 修复后：解析成功
{
    "url": "https://member.restaurantdepot.com/login",
    "content_requirements": ["名称", "价格", "图片", "upc"],
    "needs_login": true
}
```

## 关键特性

1. **智能注释检测**：只移除JSON结构外的注释，保留字符串内的内容
2. **URL保护**：不会误删URL中的`//`
3. **多种注释支持**：支持单行注释`//`和多行注释`/* */`
4. **容错性强**：即使AI仍然返回注释，系统也能正常处理
5. **向下兼容**：不影响原有的正常JSON解析

## 使用场景

这个修复特别适用于：
- Dify集成中的JSON解析错误
- AI返回格式不规范的情况
- 需要高容错性的生产环境
- 多语言内容的JSON处理

## 总结

通过这次修复，Web Scraping Agent现在能够：
- ✅ 自动处理AI返回的不规范JSON
- ✅ 智能清理注释而不影响数据内容
- ✅ 提供更稳定的JSON解析能力
- ✅ 改善用户在Dify中的使用体验

这解决了您在Dify中遇到的JSON解析失败问题，现在系统应该能够正常处理包含注释的AI响应。
