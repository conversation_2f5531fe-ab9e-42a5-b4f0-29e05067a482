version: '3.8'

services:
  web-scraping-agent:
    build: ..
    container_name: web-scraping-agent
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - API_TOKEN=${API_TOKEN:-your-secret-token}
      - HOST=0.0.0.0
      - PORT=8000
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - scraping-network

networks:
  scraping-network:
    driver: bridge

volumes:
  logs:
