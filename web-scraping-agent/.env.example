# Web Scraping Agent Environment Configuration

# OpenAI API Key (Required)
OPENAI_API_KEY=your-openai-api-key-here

# API Security Token (Optional, for API access control)
API_TOKEN=your-secret-token-here

# Server Configuration
HOST=0.0.0.0
PORT=8000

# Logging Level
LOG_LEVEL=INFO

# Scraping Configuration
DEFAULT_TIMEOUT=60
DEFAULT_WAIT_FOR_JS=3.0
DEFAULT_RETRIES=2

# User Agent
DEFAULT_USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
