# 多商品/多项目支持功能

## 概述

Web Scraping Agent现在支持智能检测和处理多商品/多项目的页面，能够根据`content_requirements`自动判断是返回单个项目还是多个项目的JSON结构。

## 核心功能

### 1. 智能检测多项目页面

系统会自动检测以下关键词来判断是否为多项目页面：

**触发多项目模式的关键词：**
- `所有商品`、`商品列表`、`多个商品`、`全部商品`
- `所有产品`、`产品列表`、`多个产品`、`全部产品`  
- `搜索结果`、`列表页`、`分类页`、`目录`
- `批量`、`集合`、`清单`

### 2. 动态JSON结构生成

#### 单项目模式（商品详情页）
```json
{
  "extracted_data": {
    "product_title": "iPhone 15 Pro Max 256GB",
    "price_info": {
      "current_price": "¥9999",
      "original_price": "¥10999"
    },
    "in_stock": true
  }
}
```

#### 多项目模式（商品列表页）
```json
{
  "extracted_data": {
    "items": [
      {
        "product_title": "iPhone 15 Pro Max 256GB",
        "price_info": "¥9999",
        "in_stock": true
      },
      {
        "product_title": "iPhone 15 Pro 128GB", 
        "price_info": "¥7999",
        "in_stock": true
      }
    ]
  }
}
```

### 3. 增强的统计信息

#### 单项目统计
```json
{
  "extraction_metadata": {
    "structure_type": "single_item",
    "total_fields": 3,
    "successful_extractions": 3,
    "failed_extractions": 0
  },
  "field_statistics": {
    "product_title": {"type": "string", "length": 25},
    "price_info": {"type": "object", "keys": 2},
    "in_stock": {"type": "boolean", "value": true}
  }
}
```

#### 多项目统计
```json
{
  "extraction_metadata": {
    "structure_type": "multiple_items",
    "item_count": 3,
    "average_completeness": 0.85
  },
  "item_statistics": [
    {
      "item_index": 0,
      "total_fields": 3,
      "successful_fields": 3,
      "completeness": 1.0
    },
    {
      "item_index": 1,
      "total_fields": 3,
      "successful_fields": 2,
      "completeness": 0.67
    }
  ]
}
```

## 使用示例

### 1. 单商品页面爬取

```python
from web_scraping_agent import create_web_scraping_agent

agent = create_web_scraping_agent(api_key)

# 单商品页面 - 自动检测为单项目模式
response = agent.scrape_website(
    description="爬取商品详情页",
    url="https://example.com/product/123",
    content_requirements=[
        "商品标题",
        "价格信息", 
        "商品描述",
        "是否有库存"
    ]
)

# 访问单商品数据
data = response.data['extracted_content']
title = data['product_title']
price = data['price_info']
in_stock = data['in_stock']
```

### 2. 多商品页面爬取

```python
# 多商品页面 - 关键词"所有"触发多项目模式
response = agent.scrape_website(
    description="爬取商品列表页",
    url="https://example.com/category/phones",
    content_requirements=[
        "所有商品标题",  # 触发多项目模式
        "商品价格",
        "商品描述", 
        "是否有库存"
    ]
)

# 访问多商品数据
data = response.data['extracted_content']
items = data['items']  # 商品列表

for i, item in enumerate(items):
    print(f"商品 {i+1}:")
    print(f"  标题: {item['product_title']}")
    print(f"  价格: {item['price_info']}")
    print(f"  库存: {item['in_stock']}")
```

### 3. 检查提取结果

```python
# 检查是否为多项目结构
if response.data['extraction_metadata']['structure_type'] == 'multiple_items':
    item_count = response.data['extraction_metadata']['item_count']
    avg_completeness = response.data['extraction_metadata']['average_completeness']
    
    print(f"找到 {item_count} 个商品")
    print(f"平均完整性: {avg_completeness:.2%}")
    
    # 查看每个商品的统计
    for stat in response.data['item_statistics']:
        completeness = stat['completeness']
        print(f"商品 {stat['item_index']}: {completeness:.2%} 完整")
else:
    print("单商品页面")
    field_stats = response.data['field_statistics']
    for field, stat in field_stats.items():
        print(f"{field}: {stat['type']}")
```

## 触发条件

### 多项目模式触发条件

在`content_requirements`中包含以下任一关键词：

1. **商品相关**: `所有商品`、`商品列表`、`多个商品`、`全部商品`
2. **产品相关**: `所有产品`、`产品列表`、`多个产品`、`全部产品`
3. **页面类型**: `搜索结果`、`列表页`、`分类页`、`目录`
4. **数量词**: `批量`、`集合`、`清单`

### 示例对比

| 需求描述 | 模式 | 原因 |
|---------|------|------|
| `["商品标题", "价格信息"]` | 单项目 | 无触发关键词 |
| `["所有商品标题", "价格信息"]` | 多项目 | 包含"所有商品" |
| `["搜索结果", "商品名称"]` | 多项目 | 包含"搜索结果" |
| `["产品列表", "产品价格"]` | 多项目 | 包含"产品列表" |

## 技术实现

### 1. 检测算法
```python
def _detect_multiple_items(self, requirements: List[str]) -> bool:
    multiple_indicators = [
        '所有商品', '商品列表', '多个商品', '全部商品',
        '所有产品', '产品列表', '多个产品', '全部产品',
        '搜索结果', '列表页', '分类页', '目录',
        '批量', '集合', '清单'
    ]
    
    full_text = ' '.join(requirements)
    return any(indicator in full_text for indicator in multiple_indicators)
```

### 2. 动态模式生成
- 单项目：直接生成字段映射
- 多项目：生成`items`数组结构，每个item包含相同字段

### 3. AI提示优化
- 单项目：标准提取提示
- 多项目：特别强调提取所有项目，使用数组结构

## 优势

1. **🎯 智能识别**: 自动判断页面类型，无需手动指定
2. **🔄 灵活结构**: 根据页面类型动态调整JSON结构
3. **📊 详细统计**: 提供项目级别的完整性分析
4. **🛡️ 向下兼容**: 不影响现有单项目功能
5. **🔍 易于调试**: 清晰的结构类型标识

这个功能使Web Scraping Agent能够处理更多样化的页面类型，无论是单个商品详情页还是包含多个商品的列表页，都能提供合适的JSON结构。
