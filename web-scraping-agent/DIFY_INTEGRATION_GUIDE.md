# 🤖 Web Scraping Agent - Dify 集成指南

## 📋 概述

本指南将帮你将智能网页爬虫系统集成到 Dify 平台中，作为自定义工具使用。

## 🚀 快速部署

### 1. 环境准备

```bash
# 克隆项目
git clone <your-repo>
cd web-scraping-agent

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，设置你的 OpenAI API Key
```

### 2. Docker 部署

```bash
# 构建并启动服务
docker-compose up -d

# 检查服务状态
docker-compose ps
docker-compose logs web-scraping-agent
```

### 3. 验证部署

```bash
# 健康检查
curl http://localhost:8000/health

# 查看 API 文档
open http://localhost:8000/docs
```

## 🔧 Dify 集成步骤

### 步骤 1: 添加自定义工具

1. 登录 Dify 管理后台
2. 进入 **工具** → **自定义工具**
3. 点击 **添加工具**
4. 选择 **API 工具**

### 步骤 2: 配置工具信息

```json
{
  "name": "web_scraping_agent",
  "label": "智能网页爬虫",
  "description": "支持自然语言输入的智能网页爬虫工具",
  "icon": "🕷️"
}
```

### 步骤 3: 配置 API 端点

```
方法: POST
URL: http://your-server:8000/tools/web-scraping
Content-Type: application/json
```

### 步骤 4: 配置参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| description | string | ✅ | 爬取需求的自然语言描述 |
| url | string | ❌ | 目标网页URL |
| username | string | ❌ | 登录用户名 |
| password | string | ❌ | 登录密码 |

### 步骤 5: 测试工具

在 Dify 中测试工具：

```json
{
  "description": "爬取百度首页的标题和搜索框信息",
  "url": "https://www.baidu.com"
}
```

## 📝 使用示例

### 示例 1: 基本网页内容提取

**输入:**
```json
{
  "description": "提取这个新闻页面的标题、作者和正文内容",
  "url": "https://news.example.com/article/123"
}
```

**输出:**
```json
{
  "status": "success",
  "data": {
    "title": "重要新闻标题",
    "author": "记者姓名",
    "content": "新闻正文内容...",
    "publish_date": "2024-01-20"
  },
  "summary": "成功提取新闻文章内容",
  "metadata": {
    "url": "https://news.example.com/article/123",
    "confidence": 0.95
  }
}
```

### 示例 2: 电商产品信息

**输入:**
```json
{
  "description": "获取商品名称、价格、库存状态和用户评分",
  "url": "https://shop.example.com/product/456"
}
```

**输出:**
```json
{
  "status": "success",
  "data": {
    "product_name": "iPhone 15 Pro",
    "price": "$999.00",
    "stock_status": "有货",
    "rating": "4.8/5",
    "reviews_count": "1,234条评论"
  },
  "summary": "成功提取商品信息",
  "metadata": {
    "url": "https://shop.example.com/product/456",
    "confidence": 0.92
  }
}
```

### 示例 3: 需要登录的网站

**输入:**
```json
{
  "description": "爬取我的订单历史记录",
  "url": "https://shop.example.com/orders",
  "username": "<EMAIL>",
  "password": "mypassword"
}
```

**输出:**
```json
{
  "status": "success",
  "data": {
    "orders": [
      {
        "order_id": "12345",
        "date": "2024-01-15",
        "total": "$299.99",
        "status": "已发货"
      }
    ]
  },
  "summary": "成功提取订单信息",
  "metadata": {
    "authenticated": true,
    "confidence": 0.88
  }
}
```

## 🎯 在 Dify 应用中使用

### 1. 工作流中使用

```yaml
# 工作流节点配置
- name: 网页爬取
  type: tool
  tool: web_scraping_agent
  inputs:
    description: "{{user_input}}"
    url: "{{target_url}}"
```

### 2. 聊天应用中使用

```yaml
# 工具配置
tools:
  - name: web_scraping_agent
    enabled: true
    parameters:
      description: "{{user_message}}"
```

### 3. Agent 应用中使用

```yaml
# Agent 工具配置
agent:
  tools:
    - web_scraping_agent
  instructions: |
    你是一个智能助手，可以帮助用户爬取网页内容。
    当用户需要获取网页信息时，使用 web_scraping_agent 工具。
```

## 🔒 安全配置

### 1. API 访问控制

```bash
# 设置访问令牌
export API_TOKEN=your-secret-token

# 在 Dify 中配置 Headers
Authorization: Bearer your-secret-token
```

### 2. 网络安全

```yaml
# docker-compose.yml
services:
  web-scraping-agent:
    networks:
      - internal
    # 不暴露到公网，仅内部访问
```

### 3. 敏感信息处理

- 密码参数标记为敏感信息
- 使用环境变量存储 API Key
- 定期轮换访问令牌

## 📊 监控和日志

### 1. 健康检查

```bash
# 检查服务状态
curl http://localhost:8000/health
```

### 2. 日志查看

```bash
# 查看实时日志
docker-compose logs -f web-scraping-agent

# 查看错误日志
docker-compose logs web-scraping-agent | grep ERROR
```

### 3. 性能监控

- API 响应时间
- 成功率统计
- 错误类型分析

## 🛠️ 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查环境变量
   docker-compose config
   
   # 查看详细错误
   docker-compose logs web-scraping-agent
   ```

2. **OpenAI API 错误**
   ```bash
   # 验证 API Key
   curl -H "Authorization: Bearer $OPENAI_API_KEY" \
        https://api.openai.com/v1/models
   ```

3. **网页爬取失败**
   ```bash
   # 检查网络连接
   docker exec web-scraping-agent curl -I https://www.baidu.com
   ```

4. **Dify 工具调用失败**
   - 检查 API 端点 URL
   - 验证参数格式
   - 查看服务日志

### 调试模式

```bash
# 启用调试日志
export LOG_LEVEL=DEBUG
docker-compose restart web-scraping-agent
```

## 🔄 更新和维护

### 1. 服务更新

```bash
# 拉取最新代码
git pull

# 重新构建和部署
docker-compose build
docker-compose up -d
```

### 2. 配置更新

```bash
# 更新环境变量
vim .env

# 重启服务
docker-compose restart web-scraping-agent
```

### 3. 备份和恢复

```bash
# 备份配置
cp .env .env.backup
cp dify_tool_config.json dify_tool_config.json.backup

# 恢复配置
cp .env.backup .env
docker-compose restart web-scraping-agent
```

## 🎉 总结

通过以上步骤，你已经成功将智能网页爬虫系统集成到 Dify 中！

### ✅ 集成优势

- 🗣️ **自然语言输入** - 用户可以用自然语言描述爬取需求
- 🔐 **智能登录** - 自动检测和处理登录需求
- 🧠 **AI 驱动** - 基于 OpenAI GPT 的智能内容提取
- 🚀 **高度通用** - 适用于各种网站和内容类型
- 📊 **结构化输出** - 返回格式化的 JSON 数据

### 🔮 扩展可能

- 添加更多网站特定的适配器
- 集成其他 LLM 模型
- 支持批量爬取任务
- 添加实时监控和告警

现在你可以在 Dify 应用中轻松使用这个强大的网页爬虫工具了！🎊
