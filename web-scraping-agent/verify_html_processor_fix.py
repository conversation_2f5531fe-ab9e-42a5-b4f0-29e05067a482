#!/usr/bin/env python3
"""
验证HTML处理器修复

快速验证HTML处理器层面的改进是否解决了空结果问题
"""

import os
import sys
import json
import asyncio
from intelligent_web_scraper import IntelligentWebScraper


async def verify_fix():
    """验证修复效果"""
    
    print("🔍 验证HTML处理器层面的修复...")
    
    # 检查API密钥
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ 请设置环境变量: export OPENAI_API_KEY='your-key'")
        return False
    
    # 创建爬虫实例
    scraper = IntelligentWebScraper(api_key)
    
    # Restaurant Depot测试用例
    user_input = """请帮我爬取这个页面的所有商品的数据
description：爬取这个商品页面的名称、价格、图片，upc等信息
URL: https://member.restaurantdepot.com/products?id=5&sort=saleranking&it=product&category=1%7cCandy%20%26%20Snacks%2c1%7cDry%20Groceries%2c1%7cJanitorial%20Supplies%2c1%7cRetail%20Groceries%20%28Food%29%2c1%7cRetail%20Groceries%20%28Non-Food%29
username：<EMAIL>
password：Jetsons823
content_requirements：title,image, item Number, upc, BIN, price"""
    
    print("📝 执行爬取测试...")
    
    try:
        result = await scraper.scrape_from_natural_language(user_input)
        
        print(f"\n📊 结果分析:")
        print(f"✅ 爬取成功: {result.success}")
        
        if result.success:
            # 检查HTML处理器的分析结果
            print(f"\n🔍 HTML处理器分析:")
            doc_metadata = result.metadata.get('document_metadata', {})
            html_analysis = doc_metadata.get('html_analysis', {})
            
            if html_analysis:
                print(f"   页面类型: {html_analysis.get('page_type', 'unknown')}")
                print(f"   检测到产品: {html_analysis.get('has_products', False)}")
                print(f"   产品指标数量: {len(html_analysis.get('product_indicators', []))}")
                
                # 显示产品指标详情
                for indicator in html_analysis.get('product_indicators', [])[:5]:
                    print(f"   - {indicator['pattern']}: {indicator['count']} 个匹配")
                
                structure_info = html_analysis.get('structure_info', {})
                print(f"   HTML结构: {structure_info.get('div_count', 0)} divs, "
                      f"{structure_info.get('img_count', 0)} images")
                
                content_quality = html_analysis.get('content_quality', {})
                print(f"   内容质量分数: {content_quality.get('complexity_score', 0)}")
            else:
                print("   ⚠️  未找到HTML分析结果")
            
            # 检查提取结果
            print(f"\n🛍️  商品提取结果:")
            extracted_data = result.extracted_data
            
            if "extracted_data" in extracted_data and "items" in extracted_data["extracted_data"]:
                items = extracted_data["extracted_data"]["items"]
                print(f"   商品数量: {len(items)}")
                
                if items:
                    print("   🎉 成功提取商品数据！")
                    
                    # 显示第一个商品示例
                    first_item = items[0]
                    print(f"   📦 第一个商品示例:")
                    for key, value in first_item.items():
                        print(f"      {key}: {value}")
                    
                    return True
                else:
                    print("   ⚠️  商品列表为空")
                    
                    # 显示调试信息
                    if "debug_info" in extracted_data:
                        debug_info = extracted_data["debug_info"]
                        print(f"\n🔧 调试信息:")
                        print(f"   可能的问题: {debug_info.get('possible_issues', [])}")
                        print(f"   内容长度: {debug_info.get('total_content_length', 0)}")
                    
                    return False
            else:
                print("   ❌ 未找到预期的数据结构")
                return False
        else:
            print(f"❌ 爬取失败: {result.error_message}")
            return False
            
    except Exception as e:
        print(f"💥 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False


def show_architecture_benefits():
    """显示架构改进的好处"""
    
    print("\n" + "=" * 60)
    print("🏗️  架构改进的好处")
    print("=" * 60)
    
    print("✅ 在HTML处理器层面进行内容分析的优势:")
    print("1. 🎯 更早发现问题 - 在HTML加载阶段就能识别问题")
    print("2. 📊 更丰富的上下文 - 提供详细的页面分析信息")
    print("3. 🚀 更好的性能 - 避免重复分析HTML内容")
    print("4. 🔧 更清晰的架构 - 关注点分离，模块化设计")
    print("5. 🔍 更强的调试能力 - 结构化的分析结果")
    
    print("\n📋 现在可以获得的信息:")
    print("• 页面类型识别（产品页、登录页、错误页等）")
    print("• 产品相关元素检测（价格、标题、UPC等）")
    print("• HTML结构分析（复杂度、元素统计）")
    print("• 内容质量评估（有意义内容、导航等）")
    
    print("\n🎯 问题诊断能力:")
    print("• 自动识别登录失败")
    print("• 检测页面加载问题")
    print("• 评估HTML结构复杂度")
    print("• 提供针对性的改进建议")


async def main():
    """主函数"""
    
    print("=" * 60)
    print("🧪 验证HTML处理器层面的修复")
    print("=" * 60)
    
    success = await verify_fix()
    
    if success:
        print("\n🎉 验证成功！HTML处理器层面的改进有效解决了问题。")
    else:
        print("\n🔧 验证显示仍有问题，但现在有更好的调试信息。")
    
    show_architecture_benefits()
    
    print("\n" + "=" * 60)
    print("✅ 验证完成")
    print("=" * 60)
    
    if not success:
        print("\n💡 下一步建议:")
        print("1. 查看HTML分析结果，确定具体问题")
        print("2. 检查登录状态和页面加载")
        print("3. 根据页面类型调整处理策略")
        print("4. 使用调试信息进行针对性优化")


if __name__ == "__main__":
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        sys.exit(1)
    
    asyncio.run(main())
