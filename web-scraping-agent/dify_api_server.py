#!/usr/bin/env python3
"""
Dify API Server for Web Scraping Agent

提供符合 Dify 规范的 HTTP API 接口
"""

import os
import logging
from typing import Dict, Any, Optional, List

from fastapi import FastAPI, HTTPException, Depends, Security
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn

from web_scraping_agent import WebScrapingAgent, AgentResponse

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建 FastAPI 应用
app = FastAPI(
    title="Web Scraping Agent API",
    description="智能网页爬取代理 API，支持 Dify 集成",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 安全认证
security = HTTPBearer()

# 全局变量
agent: Optional[WebScrapingAgent] = None


# Pydantic 模型
class ScrapingRequest(BaseModel):
    """爬取请求模型"""
    description: str = Field(..., description="自然语言描述的爬取需求")
    url: Optional[str] = Field(None, description="目标网页URL（可选）")
    username: Optional[str] = Field(None, description="登录用户名（如果需要）")
    password: Optional[str] = Field(None, description="登录密码（如果需要）")
    content_requirements: Optional[List[str]] = Field(None, description="具体内容要求列表")


class ScrapingResponse(BaseModel):
    """爬取响应模型"""
    success: bool
    data: Dict[str, Any]
    message: str
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str
    timestamp: str
    version: str
    components: Dict[str, str]


class CapabilitiesResponse(BaseModel):
    """能力描述响应模型"""
    name: str
    description: str
    version: str
    capabilities: List[str]
    supported_content_types: List[str]
    input_parameters: Dict[str, Any]
    output_format: Dict[str, str]


# 依赖注入
def get_agent() -> WebScrapingAgent:
    """获取 Agent 实例"""
    global agent
    if agent is None:
        raise HTTPException(status_code=500, detail="Agent 未初始化")
    return agent


def verify_token(credentials: HTTPAuthorizationCredentials = Security(security)) -> str:
    """验证访问令牌"""
    token = credentials.credentials
    expected_token = os.getenv('API_TOKEN')
    
    if expected_token and token != expected_token:
        raise HTTPException(
            status_code=401,
            detail="无效的访问令牌"
        )
    
    return token


# API 路由
@app.get("/", summary="根路径")
async def root():
    """根路径"""
    return {
        "service": "Web Scraping Agent API",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs"
    }


@app.get("/health", response_model=HealthResponse, summary="健康检查")
async def health_check(agent: WebScrapingAgent = Depends(get_agent)):
    """健康检查接口"""
    health_data = agent.health_check()
    return HealthResponse(**health_data)


@app.get("/capabilities", response_model=CapabilitiesResponse, summary="获取能力描述")
async def get_capabilities(agent: WebScrapingAgent = Depends(get_agent)):
    """获取 Agent 能力描述"""
    capabilities = agent.get_capabilities()
    return CapabilitiesResponse(**capabilities)


@app.post("/scrape", response_model=ScrapingResponse, summary="网页爬取")
async def scrape_website(
    request: ScrapingRequest,
    agent: WebScrapingAgent = Depends(get_agent),
    token: str = Depends(verify_token)
):
    """
    网页爬取接口
    
    支持自然语言输入，自动登录，智能内容提取
    """
    try:
        logger.info(f"收到爬取请求: {request.description}")
        
        # 调用 Agent 进行异步爬取
        response = await agent.scrape_website_async(
            description=request.description,
            url=request.url,
            username=request.username,
            password=request.password,
            content_requirements=request.content_requirements
        )
        
        # 转换为 Pydantic 模型
        return ScrapingResponse(
            success=response.success,
            data=response.data,
            message=response.message,
            error=response.error,
            metadata=response.metadata
        )
        
    except Exception as e:
        logger.error(f"爬取请求处理失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


class SimpleScrapingRequest(BaseModel):
    """简化爬取请求模型"""
    description: str = Field(..., description="爬取需求描述")
    url: Optional[str] = Field(None, description="目标URL")


@app.post("/scrape/simple", summary="简化爬取接口")
async def scrape_simple(
    request: SimpleScrapingRequest,
    agent: WebScrapingAgent = Depends(get_agent),
    token: str = Depends(verify_token)
):
    """
    简化的爬取接口，适用于 Dify 工具调用
    """
    try:
        response = await agent.scrape_website_async(description=request.description, url=request.url)
        
        # 返回简化的响应格式
        if response.success:
            return {
                "success": True,
                "result": response.data.get('extracted_content', {}),
                "summary": response.message,
                "url": response.data.get('url', url)
            }
        else:
            return {
                "success": False,
                "error": response.error,
                "message": response.message
            }
            
    except Exception as e:
        logger.error(f"简化爬取请求处理失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "爬取失败"
        }


class DifyToolRequest(BaseModel):
    """Dify 工具请求模型"""
    description: str = Field(..., description="爬取需求的自然语言描述")
    url: Optional[str] = Field(None, description="目标网页URL")
    username: Optional[str] = Field(None, description="登录用户名")
    password: Optional[str] = Field(None, description="登录密码")


# Dify 工具规范接口
@app.post("/tools/web-scraping", summary="Dify 工具接口")
async def dify_tool_interface(
    request: DifyToolRequest,
    agent: WebScrapingAgent = Depends(get_agent)
):
    """
    符合 Dify 工具规范的接口
    
    这个接口专门为 Dify 平台设计，提供标准化的输入输出格式
    """
    try:
        # 执行爬取
        response = await agent.scrape_website_async(
            description=request.description,
            url=request.url,
            username=request.username,
            password=request.password
        )
        
        if response.success:
            # 成功时返回提取的内容
            extracted_content = response.data.get('extracted_content', {})
            
            # 格式化为 Dify 期望的格式
            result = {
                "status": "success",
                "data": extracted_content,
                "summary": response.message,
                "metadata": {
                    "url": response.data.get('url'),
                    "confidence": response.data.get('confidence', 0),
                    "content_length": response.data.get('content_length', 0)
                }
            }
            
            return result
        else:
            # 失败时返回错误信息
            return {
                "status": "error",
                "error": response.error,
                "message": response.message,
                "metadata": {
                    "url": response.data.get('url')
                }
            }
            
    except Exception as e:
        logger.error(f"Dify 工具接口处理失败: {e}")
        return {
            "status": "error",
            "error": str(e),
            "message": "工具执行失败"
        }


# 启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动时初始化"""
    global agent
    
    # 获取 OpenAI API Key
    openai_api_key = os.getenv('OPENAI_API_KEY')
    if not openai_api_key:
        logger.error("未设置 OPENAI_API_KEY 环境变量")
        raise RuntimeError("OPENAI_API_KEY is required")
    
    # 初始化 Agent
    try:
        agent = WebScrapingAgent(openai_api_key)
        logger.info("Web Scraping Agent 初始化成功")
    except Exception as e:
        logger.error(f"Agent 初始化失败: {e}")
        raise RuntimeError(f"Failed to initialize agent: {e}")


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时清理"""
    logger.info("Web Scraping Agent API 服务关闭")


# 主函数
def main():
    """启动服务"""
    # 配置参数
    host = os.getenv('HOST', '0.0.0.0')
    port = int(os.getenv('PORT', '8000'))
    
    # 启动服务
    uvicorn.run(
        "dify_api_server:app",
        host=host,
        port=port,
        reload=False,
        log_level="info"
    )


if __name__ == "__main__":
    main()
