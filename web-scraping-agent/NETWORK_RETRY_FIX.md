# 网络重试机制修复

## 问题描述

用户在使用Web Scraping Agent时遇到网络连接错误：

```
2025-07-22 11:38:23,351 - openai._base_client - INFO - Retrying request to /chat/completions in 0.876836 seconds
2025-07-22 11:38:40,908 - openai._base_client - INFO - Retrying request to /chat/completions in 1.809755 seconds
2025-07-22 11:38:59,258 - intelligent_web_scraper - ERROR - Content extraction failed: Connection error.
```

这表明OpenAI API调用出现了网络问题，导致整个爬取过程失败。

## 问题根因

1. **网络不稳定**: 网络连接不稳定导致API请求超时或连接失败
2. **缺乏重试机制**: 系统没有自动重试机制，一次失败就直接返回错误
3. **超时设置不当**: 没有合适的超时设置
4. **错误处理不完善**: 没有区分网络错误和其他类型的错误

## 解决方案

### 1. 添加智能重试机制

为`IntentAnalyzer`和`ContentExtractor`都添加了重试逻辑：

```python
# 添加重试机制
max_retries = 3
retry_delay = 2

for attempt in range(max_retries):
    try:
        logger.info(f"尝试第 {attempt + 1} 次内容提取...")
        
        response = self.client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[...],
            temperature=0.1,
            timeout=60  # 设置超时
        )
        
        # 处理成功响应
        return result
        
    except Exception as e:
        error_msg = str(e)
        logger.warning(f"第 {attempt + 1} 次尝试失败: {error_msg}")
        
        # 检查是否是网络错误
        if any(keyword in error_msg.lower() for keyword in 
               ["connection", "timeout", "network", "read timeout", "connect timeout"]):
            if attempt < max_retries - 1:
                logger.info(f"检测到网络问题，{retry_delay} 秒后重试...")
                time.sleep(retry_delay)
                retry_delay *= 2  # 指数退避
                continue
        
        # 最后一次尝试失败
        if attempt == max_retries - 1:
            logger.error(f"所有重试均失败，最终错误: {error_msg}")
            return error_response
```

### 2. 指数退避策略

采用指数退避策略减少服务器压力：
- 第1次重试: 等待2秒
- 第2次重试: 等待4秒  
- 第3次重试: 等待8秒

### 3. 智能错误检测

系统会检测以下网络相关错误关键词：
- `connection`
- `timeout`
- `network`
- `read timeout`
- `connect timeout`

只有网络相关错误才会触发重试，其他错误（如API密钥错误）会立即返回。

### 4. 超时设置优化

- **意图分析**: 30秒超时
- **内容提取**: 60秒超时

### 5. 详细日志记录

```
INFO - 尝试第 1 次内容提取...
WARNING - 第 1 次尝试失败: Connection error
INFO - 检测到网络问题，2 秒后重试...
INFO - 尝试第 2 次内容提取...
INFO - 成功获取AI响应，长度: 1024 字符
INFO - 成功解析AI响应为JSON格式
```

## 修复效果

### 修复前
- ❌ 网络错误立即失败
- ❌ 用户体验差
- ❌ 需要手动重试
- ❌ 成功率低

### 修复后
- ✅ 自动重试网络错误
- ✅ 指数退避策略
- ✅ 智能错误分类
- ✅ 详细重试日志
- ✅ 更高的成功率

## 重试流程

1. **第一次尝试**: 正常发送API请求
2. **检测错误**: 如果失败，检查错误类型
3. **判断重试**: 如果是网络错误且未达到最大重试次数
4. **等待重试**: 按指数退避策略等待
5. **重新尝试**: 发送新的API请求
6. **重复流程**: 直到成功或达到最大重试次数

## 错误分类

### 会触发重试的错误
- Connection error
- Read timeout
- Connect timeout
- Network error
- 其他包含网络关键词的错误

### 不会触发重试的错误
- API密钥错误
- 模型不存在
- 请求格式错误
- 权限错误
- JSON解析错误

## 配置参数

```python
# 重试配置
max_retries = 3          # 最大重试次数
initial_delay = 2        # 初始延迟时间(秒)
timeout_intent = 30      # 意图分析超时(秒)
timeout_extract = 60     # 内容提取超时(秒)

# 错误检测关键词
network_keywords = [
    "connection", "timeout", "network", 
    "read timeout", "connect timeout"
]
```

## 使用场景

这个重试机制特别适用于：

1. **网络不稳定环境**
   - 移动网络
   - 公共WiFi
   - 跨国网络访问

2. **高负载情况**
   - API服务器繁忙
   - 网络拥塞
   - 临时服务中断

3. **生产环境**
   - 需要高可靠性
   - 自动化任务
   - 批量处理

## 监控和调试

### 日志级别
- `INFO`: 正常重试信息
- `WARNING`: 单次尝试失败
- `ERROR`: 所有重试失败

### 关键指标
- 重试次数
- 成功率
- 平均响应时间
- 错误类型分布

## 总结

通过添加智能重试机制，Web Scraping Agent现在能够：

1. **自动处理网络错误** - 无需用户干预
2. **提高成功率** - 通过多次重试增加成功概率
3. **优化用户体验** - 减少因网络问题导致的失败
4. **智能错误处理** - 区分不同类型的错误
5. **详细日志记录** - 便于问题诊断和监控

这解决了您遇到的"Connection error"问题，使系统在网络不稳定的情况下也能正常工作。
