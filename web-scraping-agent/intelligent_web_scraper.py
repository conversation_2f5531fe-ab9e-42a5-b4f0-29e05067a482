#!/usr/bin/env python3
"""
智能网页爬虫系统

基于自然语言输入和 OpenAI LLM 的智能爬虫，支持：
- 自然语言意图理解
- 智能登录检测
- 自动内容提取
- AI 驱动的结果解析
"""

import asyncio
import json
import logging
import os
import re
import time as time_module
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from dataclasses import dataclass

# HTML processing libraries
try:
    import htmlmin
except ImportError:
    htmlmin = None

try:
    import html2text
except ImportError:
    html2text = None

try:
    from bs4 import BeautifulSoup
except ImportError:
    BeautifulSoup = None

try:
    import openai
    from openai import OpenAI
except ImportError:
    openai = None
    OpenAI = None

from langchain_html_processor import (
    AuthenticatedHTMLLoader,
    SmartHTMLLoader,
    LoaderConfig
)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class ScrapingIntent:
    """爬取意图数据结构"""
    url: str
    content_requirements: List[str]
    needs_login: bool
    username: Optional[str] = None
    password: Optional[str] = None
    login_url: Optional[str] = None
    additional_info: Dict[str, Any] = None


@dataclass
class ScrapingResult:
    """爬取结果数据结构"""
    success: bool
    url: str
    extracted_data: Dict[str, Any]
    raw_content: str
    metadata: Dict[str, Any]
    error_message: Optional[str] = None


class IntentAnalyzer:
    """自然语言意图分析器"""

    def __init__(self, openai_client: OpenAI):
        self.client = openai_client

    def _clean_json_response(self, json_text: str) -> str:
        """清理AI返回的JSON中的注释和多余内容"""
        import re

        # 更精确的注释移除：只移除不在字符串内的注释
        lines = json_text.split('\n')
        cleaned_lines = []

        for line in lines:
            # 检查是否在字符串内
            in_string = False
            escape_next = False
            comment_start = -1

            for i, char in enumerate(line):
                if escape_next:
                    escape_next = False
                    continue

                if char == '\\':
                    escape_next = True
                    continue

                if char == '"' and not escape_next:
                    in_string = not in_string
                    continue

                # 只在字符串外查找注释
                if not in_string:
                    if i < len(line) - 1 and line[i:i+2] == '//':
                        comment_start = i
                        break

            # 移除注释部分
            if comment_start >= 0:
                line = line[:comment_start].rstrip()

            cleaned_lines.append(line)

        # 重新组合
        json_text = '\n'.join(cleaned_lines)

        # 移除多行注释 /* ... */ (简单处理，假设不在字符串内)
        json_text = re.sub(r'/\*.*?\*/', '', json_text, flags=re.DOTALL)

        # 清理多余的空白字符
        json_text = re.sub(r'\n\s*\n', '\n', json_text)

        return json_text.strip()
    
    def analyze_intent(self, user_input: str) -> ScrapingIntent:
        """分析用户的自然语言输入，提取爬取意图"""
        
        system_prompt = """
你是一个智能网页爬虫助手。分析用户的自然语言输入，提取以下信息：

1. 目标URL（如果提供）
2. 需要爬取的内容类型（如：价格、标题、评论、图片等）
3. 是否需要登录
4. 登录信息（如果提供）
5. 其他相关信息

请以JSON格式返回结果（注意：不要在JSON中包含任何注释）：
{
    "url": "目标网址",
    "content_requirements": ["需要爬取的内容1", "内容2"],
    "needs_login": true,
    "username": "用户名（如果提供）",
    "password": "密码（如果提供）",
    "login_url": "登录页面URL（如果不同于主URL）",
    "additional_info": {
        "其他": "相关信息"
    }
}

重要：返回的必须是纯净的JSON格式，不要包含任何注释（//或/**/）。

如果信息不完整，在additional_info中说明需要用户补充什么信息。
"""
        
        # 添加重试机制
        max_retries = 1
        retry_delay = 2

        logger.info(f"提取爬取意图 system prompt: {system_prompt}")

        for attempt in range(max_retries):
            try:
                logger.info(f"尝试第 {attempt + 1} 次意图分析...")

                # 在重试时添加延迟
                if attempt > 0:
                    logger.info(f"等待 {retry_delay} 秒后重试...")
                    time_module.sleep(retry_delay)

                response = self.client.chat.completions.create(
                    model="gpt-4.1-mini",  # 改为更稳定的模型
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_input}
                    ],
                    temperature=0.1,
                    timeout=45  # 增加超时时间到45秒
                )

                result_text = response.choices[0].message.content
                logger.info(f"成功获取意图分析响应，长度: {len(result_text)} 字符")

                # 尝试解析JSON
                try:
                    # 清理JSON中的注释
                    cleaned_json = self._clean_json_response(result_text)
                    result_json = json.loads(cleaned_json)
                    logger.info("成功解析意图分析JSON")
                    return ScrapingIntent(
                        url=result_json.get('url', ''),
                        content_requirements=result_json.get('content_requirements', []),
                        needs_login=result_json.get('needs_login', False),
                        username=result_json.get('username'),
                        password=result_json.get('password'),
                        login_url=result_json.get('login_url'),
                        additional_info=result_json.get('additional_info', {})
                    )
                except json.JSONDecodeError:
                    logger.error(f"Failed to parse AI response as JSON: {result_text}")
                    return ScrapingIntent(
                        url='',
                        content_requirements=[],
                        needs_login=False,
                        additional_info={'error': 'AI响应解析失败', 'raw_response': result_text}
                    )

            except Exception as e:
                error_msg = str(e)
                logger.warning(f"意图分析第 {attempt + 1} 次尝试失败: {error_msg}")

                # 检查是否是连接错误
                if any(keyword in error_msg.lower() for keyword in ["connection", "timeout", "network", "read timeout", "connect timeout"]):
                    if attempt < max_retries - 1:
                        logger.info(f"检测到网络问题，{retry_delay} 秒后重试...")
                        time_module.sleep(retry_delay)
                        retry_delay *= 2  # 指数退避
                        continue

                # 如果是最后一次尝试或非网络错误，返回错误
                if attempt == max_retries - 1:
                    logger.error(f"意图分析所有重试均失败，最终错误: {error_msg}")
                    return ScrapingIntent(
                        url='',
                        content_requirements=[],
                        needs_login=False,
                        additional_info={'error': f'意图分析失败: {str(e)}'}
                    )

        # 理论上不会到达这里
        return ScrapingIntent(
            url='',
            content_requirements=[],
            needs_login=False,
            additional_info={'error': '意图分析失败: 未知错误'}
        )


class ContentExtractor:
    """智能内容提取器"""

    def __init__(self, openai_client: OpenAI):
        self.client = openai_client

    def _clean_json_response(self, json_text: str) -> str:
        """清理AI返回的JSON中的注释和多余内容"""
        import re

        # 更精确的注释移除：只移除不在字符串内的注释
        lines = json_text.split('\n')
        cleaned_lines = []

        for line in lines:
            # 检查是否在字符串内
            in_string = False
            escape_next = False
            comment_start = -1

            for i, char in enumerate(line):
                if escape_next:
                    escape_next = False
                    continue

                if char == '\\':
                    escape_next = True
                    continue

                if char == '"' and not escape_next:
                    in_string = not in_string
                    continue

                # 只在字符串外查找注释
                if not in_string:
                    if i < len(line) - 1 and line[i:i+2] == '//':
                        comment_start = i
                        break

            # 移除注释部分
            if comment_start >= 0:
                line = line[:comment_start].rstrip()

            cleaned_lines.append(line)

        # 重新组合
        json_text = '\n'.join(cleaned_lines)

        # 移除多行注释 /* ... */ (简单处理，假设不在字符串内)
        json_text = re.sub(r'/\*.*?\*/', '', json_text, flags=re.DOTALL)

        # 清理多余的空白字符
        json_text = re.sub(r'\n\s*\n', '\n', json_text)

        return json_text.strip()

    def _clean_html_content(self, html_content: str) -> str:
        """清理HTML内容，移除无用标签和内容以减少大小"""
        import re

        logger.info(f"移除前 html_content长度: {len(html_content)} 字符")

        # 移除script标签及其内容
        html_content = re.sub(r'<script[^>]*>.*?</script>', '', html_content, flags=re.DOTALL | re.IGNORECASE)

        # 移除style标签及其内容
        html_content = re.sub(r'<style[^>]*>.*?</style>', '', html_content, flags=re.DOTALL | re.IGNORECASE)

        # 移除HTML注释
        html_content = re.sub(r'<!--.*?-->', '', html_content, flags=re.DOTALL)

        # 移除多余的空白字符
        html_content = re.sub(r'\s+', ' ', html_content)
        html_content = re.sub(r'\n\s*\n', '\n', html_content)

        # 移除一些常见的无用属性
        html_content = re.sub(r'\s(style|onclick|onload|onerror)="[^"]*"', '', html_content, flags=re.IGNORECASE)

        logger.info(f"移除后 html_content长度: {len(html_content)} 字符")
        return html_content.strip()

    def _compress_html_content(self, html_content: str, compression_level: str = "aggressive") -> str:
        """增强的HTML压缩管道，集成多种压缩技术"""
        import re

        original_length = len(html_content)
        logger.info(f"🗜️  开始增强HTML压缩管道，原始长度: {original_length} 字符")

        # 第一步：基础清理
        html_content = self._clean_html_content(html_content)
        step1_length = len(html_content)
        logger.info(f"📊 步骤1-基础清理: {original_length} -> {step1_length} 字符")

        # 第二步：htmlmin压缩
        html_content = self._htmlmin_compress(html_content)
        step2_length = len(html_content)
        logger.info(f"📊 步骤2-htmlmin压缩: {step1_length} -> {step2_length} 字符")

        # 第三步：BeautifulSoup智能提取
        html_content = self._beautifulsoup_extract_smart(html_content)
        step3_length = len(html_content)
        logger.info(f"📊 步骤3-智能提取: {step2_length} -> {step3_length} 字符")

        # 第四步：检查大小，如果仍然过大则使用html2text
        max_size_threshold = 8000  # 8KB阈值
        if step3_length > max_size_threshold:
            logger.warning(f"⚠️  HTML仍然过大({step3_length}字符)，使用html2text转换")
            html_content = self._html2text_fallback(html_content)
            step4_length = len(html_content)
            logger.info(f"📊 步骤4-html2text转换: {step3_length} -> {step4_length} 字符")
        else:
            step4_length = step3_length
            logger.info(f"📊 步骤4-跳过html2text转换，大小适中: {step4_length} 字符")

        # 第五步：最终清理和压缩
        if compression_level == "aggressive":
            # 最终的空白字符压缩
            html_content = re.sub(r'\s+', ' ', html_content)
            html_content = re.sub(r'>\s+<', '><', html_content)
            html_content = re.sub(r'\n\s*\n', '\n', html_content)
            html_content = html_content.strip()

        # 计算最终压缩效果
        final_length = len(html_content)
        total_compression_ratio = (original_length - final_length) / original_length * 100

        logger.info(f"🗜️  增强HTML压缩管道完成: {original_length} -> {final_length} 字符 (总压缩率: {total_compression_ratio:.1f}%)")

        return html_content

    def _extract_product_sections(self, html_content: str) -> str:
        """提取包含商品信息的HTML片段，进一步减少内容大小"""
        import re

        logger.info("🎯 尝试提取核心商品信息片段")

        # 查找包含商品信息的主要容器
        product_containers = []

        # 常见的商品容器模式
        container_patterns = [
            r'<div[^>]*class="[^"]*product[^"]*"[^>]*>.*?</div>',
            r'<div[^>]*class="[^"]*item[^"]*"[^>]*>.*?</div>',
            r'<div[^>]*class="[^"]*card[^"]*"[^>]*>.*?</div>',
            r'<article[^>]*>.*?</article>',
            r'<li[^>]*class="[^"]*product[^"]*"[^>]*>.*?</li>',
            r'<li[^>]*class="[^"]*item[^"]*"[^>]*>.*?</li>',
        ]

        for pattern in container_patterns:
            matches = re.findall(pattern, html_content, flags=re.DOTALL | re.IGNORECASE)
            if matches:
                product_containers.extend(matches)
                logger.info(f"找到 {len(matches)} 个匹配模式: {pattern[:50]}...")

        if product_containers:
            # 如果找到商品容器，只保留这些容器的内容
            extracted_content = ' '.join(product_containers)
            logger.info(f"🎯 成功提取商品片段: {len(html_content)} -> {len(extracted_content)} 字符")
            return extracted_content
        else:
            # 如果没有找到明确的商品容器，尝试提取包含价格信息的区域
            price_sections = re.findall(r'<[^>]*>\$\d+\.\d+.*?</[^>]*>', html_content, flags=re.DOTALL | re.IGNORECASE)
            if price_sections:
                extracted_content = ' '.join(price_sections)
                logger.info(f"🎯 提取价格相关片段: {len(html_content)} -> {len(extracted_content)} 字符")
                return extracted_content
            else:
                logger.info("🎯 未找到明确的商品片段，返回压缩后的完整内容")
                return html_content

    def _htmlmin_compress(self, html_content: str) -> str:
        """使用htmlmin进行基础HTML压缩"""
        if htmlmin is None:
            logger.warning("htmlmin未安装，跳过htmlmin压缩")
            return html_content

        original_length = len(html_content)
        logger.info(f"🗜️  开始htmlmin压缩，原始长度: {original_length} 字符")

        try:
            # htmlmin压缩选项
            compressed_html = htmlmin.minify(
                html_content,
                remove_comments=True,
                remove_empty_space=True,
                remove_all_empty_space=False,  # 保留一些空格以维持可读性
                reduce_empty_attributes=True,
                reduce_boolean_attributes=True,
                remove_optional_attribute_quotes=False,  # 保留引号以避免解析问题
                convert_charrefs=True,
                keep_pre=True  # 保留pre标签中的格式
            )

            compressed_length = len(compressed_html)
            compression_ratio = (original_length - compressed_length) / original_length * 100
            logger.info(f"🗜️  htmlmin压缩完成: {original_length} -> {compressed_length} 字符 (压缩率: {compression_ratio:.1f}%)")

            return compressed_html

        except Exception as e:
            logger.warning(f"htmlmin压缩失败: {e}，返回原始内容")
            return html_content

    def _beautifulsoup_extract_smart(self, html_content: str) -> str:
        """使用BeautifulSoup进行智能商品信息提取"""
        if BeautifulSoup is None:
            logger.warning("BeautifulSoup未安装，跳过智能提取")
            return html_content

        original_length = len(html_content)
        logger.info(f"🧠 开始BeautifulSoup智能提取，原始长度: {original_length} 字符")

        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # 移除不需要的标签
            unwanted_tags = ['script', 'style', 'nav', 'footer', 'header', 'aside', 'noscript']
            for tag_name in unwanted_tags:
                for tag in soup.find_all(tag_name):
                    tag.decompose()

            # 移除不需要的class和id
            unwanted_classes = ['advertisement', 'ad', 'banner', 'popup', 'modal', 'cookie', 'newsletter']
            for class_name in unwanted_classes:
                for element in soup.find_all(class_=lambda x: x and any(unwanted in str(x).lower() for unwanted in unwanted_classes)):
                    element.decompose()

            # 查找商品相关的容器
            product_selectors = [
                '[class*="product"]',
                '[class*="item"]',
                '[class*="card"]',
                '[data-product]',
                '[data-item]',
                'article',
                '.product-list li',
                '.item-list li'
            ]

            product_elements = []
            for selector in product_selectors:
                elements = soup.select(selector)
                if elements:
                    product_elements.extend(elements)
                    logger.info(f"找到 {len(elements)} 个元素匹配选择器: {selector}")

            if product_elements:
                # 如果找到商品元素，只保留这些元素
                extracted_soup = BeautifulSoup('<div></div>', 'html.parser')
                container = extracted_soup.div

                for element in product_elements[:50]:  # 限制最多50个商品，避免过大
                    container.append(element.extract())

                extracted_html = str(extracted_soup)
                extracted_length = len(extracted_html)
                compression_ratio = (original_length - extracted_length) / original_length * 100

                logger.info(f"🧠 BeautifulSoup智能提取完成: {original_length} -> {extracted_length} 字符 (压缩率: {compression_ratio:.1f}%)")
                return extracted_html
            else:
                # 如果没有找到明确的商品元素，移除无用内容后返回
                cleaned_html = str(soup)
                cleaned_length = len(cleaned_html)
                compression_ratio = (original_length - cleaned_length) / original_length * 100

                logger.info(f"🧠 BeautifulSoup清理完成: {original_length} -> {cleaned_length} 字符 (压缩率: {compression_ratio:.1f}%)")
                return cleaned_html

        except Exception as e:
            logger.warning(f"BeautifulSoup处理失败: {e}，返回原始内容")
            return html_content

    def _html2text_fallback(self, html_content: str) -> str:
        """使用html2text作为极端情况的备选方案"""
        if html2text is None:
            logger.warning("html2text未安装，跳过文本转换")
            return html_content

        original_length = len(html_content)
        logger.info(f"📝 开始html2text转换，原始长度: {original_length} 字符")

        try:
            h = html2text.HTML2Text()
            h.ignore_links = False  # 保留链接，可能包含商品URL
            h.ignore_images = False  # 保留图片信息
            h.ignore_emphasis = True  # 忽略强调标记
            h.body_width = 0  # 不限制行宽
            h.unicode_snob = True  # 使用Unicode

            text_content = h.handle(html_content)

            # 清理多余的空行
            text_content = re.sub(r'\n\s*\n\s*\n', '\n\n', text_content)
            text_content = text_content.strip()

            text_length = len(text_content)
            compression_ratio = (original_length - text_length) / original_length * 100

            logger.info(f"📝 html2text转换完成: {original_length} -> {text_length} 字符 (压缩率: {compression_ratio:.1f}%)")
            return text_content

        except Exception as e:
            logger.warning(f"html2text转换失败: {e}，返回原始内容")
            return html_content

    def _optimize_system_prompt(self, requirements: List[str], url: str, is_multiple_items: bool) -> str:
        """优化system prompt，减少不必要的文字以节省token"""

        if is_multiple_items:
            # 简化的多项目prompt
            prompt = f"""你是专业网页数据提取器。从HTML中提取所有商品的{', '.join(requirements)}。

任务：提取页面所有商品信息
URL：{url}

返回JSON格式：
{{
    "extracted_data": {{
        "items": [
            {{"field1": "value1", "field2": "value2"}},
            {{"field1": "value1", "field2": "value2"}}
        ]
    }},
    "summary": "找到X个商品",
    "confidence": 0.8,
    "notes": "说明"
}}

要求：
1. 提取所有商品，放入items数组
2. 缺失字段设为null
3. 返回有效JSON，无注释
4. 在summary中说明商品数量"""
        else:
            # 简化的单项目prompt
            prompt = f"""你是智能内容提取器。从HTML中提取{', '.join(requirements)}。

URL：{url}

返回JSON格式：
{{
    "extracted_data": {{"field1": "value1", "field2": "value2"}},
    "summary": "提取结果总结",
    "confidence": 0.8,
    "notes": "说明"
}}

要求：
1. 使用指定字段名
2. 保留完整格式（价格、链接等）
3. 缺失内容设为null
4. 返回有效JSON，无注释"""

        return prompt

    def _sanitize_field_name(self, requirement: str) -> str:
        """将需求转换为有效的JSON字段名"""
        import re

        # 常见的中文到英文映射（按长度排序，优先匹配长词）
        field_mappings = {
            # 完整短语映射（优先级最高）
            '所有商品标题': 'all_product_titles',
            '商品标题': 'product_title',
            '用户评论列表': 'user_reviews',
            '商品规格属性': 'product_specifications',
            '商品详细描述': 'product_description',
            '商品图片链接': 'product_images',
            '卖家联系方式': 'seller_contact',
            '商品分类标签': 'product_category_tags',
            '是否有库存': 'in_stock',
            '价格信息': 'price_info',
            '联系方式': 'contact_info',
            '联系电话': 'contact_phone',
            '公司地址': 'company_address',
            '营业时间': 'business_hours',
            '发布时间': 'publish_time',
            '评分数值': 'rating_score',
            '文章标题': 'article_title',
            '文章内容': 'article_content',
            '作者信息': 'author_info',
            '相关标签': 'related_tags',
            '阅读数量': 'read_count',
            '公司名称': 'company_name',
            '邮箱地址': 'email_address',
            '服务项目列表': 'service_list',
            # 单词映射（优先级较低）
            '所有': 'all',
            '商品': 'product',
            '标题': 'title',
            '名称': 'name',
            '价格': 'price',
            '描述': 'description',
            '评论': 'reviews',
            '评价': 'reviews',
            '评分': 'rating',
            '库存': 'stock',
            '图片': 'images',
            '链接': 'links',
            '地址': 'address',
            '电话': 'phone',
            '邮箱': 'email',
            '时间': 'time',
            '日期': 'date',
            '作者': 'author',
            '分类': 'category',
            '标签': 'tags',
            '数量': 'quantity',
            '规格': 'specifications',
            '特征': 'features',
            '属性': 'attributes',
            '详细': 'details',
            '信息': 'info',
            '内容': 'content',
            '文章': 'article',
            '公司': 'company',
            '服务': 'service',
            '项目': 'item',
            '列表': 'list'
        }

        # 按长度排序，优先匹配长词
        sorted_mappings = sorted(field_mappings.items(), key=lambda x: len(x[0]), reverse=True)

        field_name = requirement
        # 尝试映射常见词汇
        for chinese, english in sorted_mappings:
            if chinese in field_name:
                field_name = field_name.replace(chinese, english)
                break  # 找到第一个匹配就停止

        # 移除特殊字符，转换为snake_case
        field_name = re.sub(r'[^\w\s]', '', field_name)
        field_name = re.sub(r'\s+', '_', field_name.strip())
        field_name = field_name.lower()

        # 清理多余的下划线
        field_name = re.sub(r'_+', '_', field_name)
        field_name = field_name.strip('_')

        # 如果结果为空或只有数字，使用默认名称
        if not field_name or field_name.isdigit():
            field_name = 'content_item'

        return field_name

    def _determine_data_type(self, requirement: str) -> tuple[str, str]:
        """根据需求确定数据类型和示例"""
        requirement_lower = requirement.lower()

        # 列表类型关键词
        list_keywords = ['列表', '所有', '多个', '清单', '全部', '批量', '集合']
        # 对象类型关键词
        object_keywords = ['详细信息', '详情', '属性', '特征', '规格', '参数']
        # 数字类型关键词
        number_keywords = ['价格', '数量', '评分', '分数', '金额', '费用', '成本']
        # 布尔类型关键词
        boolean_keywords = ['是否', '有无', '能否', '可否']

        if any(keyword in requirement for keyword in list_keywords):
            return 'array', '["项目1", "项目2", "项目3"]'
        elif any(keyword in requirement for keyword in object_keywords):
            return 'object', '{"属性1": "值1", "属性2": "值2"}'
        elif any(keyword in requirement for keyword in number_keywords):
            return 'number', '99.99'
        elif any(keyword in requirement for keyword in boolean_keywords):
            return 'boolean', 'true'
        else:
            return 'string', '"文本内容"'

    def _detect_multiple_items(self, requirements: List[str], url: str = "", description: str = "") -> bool:
        """检测是否可能返回多个项目（如多个商品）"""
        # 基于需求的关键词检测
        requirement_indicators = [
            '所有商品', '商品列表', '多个商品', '全部商品',
            '所有产品', '产品列表', '多个产品', '全部产品',
            '搜索结果', '列表页', '分类页', '目录',
            '批量', '集合', '清单'
        ]

        # 基于URL的模式检测
        url_indicators = [
            'products', 'category', 'search', 'list', 'catalog',
            'browse', 'shop', 'store', 'items', 'goods'
        ]

        # 基于描述的关键词检测
        description_indicators = [
            '商品数据', '产品数据', '所有', '全部', '列表',
            '多个', '批量', '页面', 'products', 'items'
        ]

        # 检查需求文本
        full_requirements_text = ' '.join(requirements)
        requirements_match = any(indicator in full_requirements_text for indicator in requirement_indicators)

        # 检查URL
        url_lower = url.lower()
        url_match = any(indicator in url_lower for indicator in url_indicators)

        # 检查描述
        description_lower = description.lower()
        description_match = any(indicator in description_lower for indicator in description_indicators)

        # 特殊URL模式检测（如包含category参数的URL）
        special_url_patterns = [
            'category=', 'cat=', 'type=', 'sort=', 'filter=',
            'page=', 'limit=', 'offset=', 'search=', '?s?', '?k=', '&k='
        ]
        special_url_match = any(pattern in url_lower for pattern in special_url_patterns)

        # 如果任一条件满足，则认为是多项目页面
        return requirements_match or url_match or description_match or special_url_match

    def _generate_dynamic_schema(self, requirements: List[str], url: str = "", description: str = "") -> Dict[str, Any]:
        """根据需求生成动态JSON模式"""
        # 检测是否可能返回多个项目
        is_multiple_items = self._detect_multiple_items(requirements, url, description)

        schema = {
            "type": "object",
            "properties": {},
            "field_examples": {},
            "is_multiple_items": is_multiple_items
        }

        if is_multiple_items:
            # 多项目模式：创建items数组结构
            item_schema = {}
            item_examples = {}

            for requirement in requirements:
                field_name = self._sanitize_field_name(requirement)
                data_type, example = self._determine_data_type(requirement)

                item_schema[field_name] = {
                    "type": data_type,
                    "description": requirement,
                    "original_requirement": requirement
                }
                item_examples[field_name] = example

            schema["properties"]["items"] = {
                "type": "array",
                "description": "项目列表（如商品列表、产品列表等）",
                "item_schema": item_schema
            }
            schema["field_examples"]["items"] = [item_examples, item_examples]  # 示例包含2个项目

        else:
            # 单项目模式：直接创建字段
            for requirement in requirements:
                field_name = self._sanitize_field_name(requirement)
                data_type, example = self._determine_data_type(requirement)

                schema["properties"][field_name] = {
                    "type": data_type,
                    "description": requirement,
                    "original_requirement": requirement
                }
                schema["field_examples"][field_name] = example

        return schema

    def _analyze_html_content(self, html_content: str) -> Dict[str, Any]:
        """分析HTML内容，检测是否包含产品信息"""
        import re

        analysis = {
            "has_products": False,
            "product_indicators": [],
            "structure_info": {},
            "content_preview": ""
        }

        # 常见的产品相关HTML模式
        product_patterns = [
            (r'class="[^"]*product[^"]*"', "product class"),
            (r'class="[^"]*item[^"]*"', "item class"),
            (r'class="[^"]*card[^"]*"', "card class"),
            (r'data-product', "product data attribute"),
            (r'data-item', "item data attribute"),
            (r'\$\d+\.\d+', "price pattern"),
            (r'price["\s]*[:=]', "price field"),
            (r'title["\s]*[:=]', "title field"),
            (r'<img[^>]*src', "image tags"),
            (r'upc["\s]*[:=]', "UPC field"),
            (r'sku["\s]*[:=]', "SKU field"),
        ]

        for pattern, description in product_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            if matches:
                analysis["product_indicators"].append({
                    "pattern": description,
                    "count": len(matches),
                    "examples": matches[:3]  # 前3个示例
                })
                analysis["has_products"] = True

        # 分析HTML结构
        analysis["structure_info"] = {
            "total_length": len(html_content),
            "div_count": len(re.findall(r'<div', html_content, re.IGNORECASE)),
            "class_count": len(re.findall(r'class=', html_content, re.IGNORECASE)),
            "img_count": len(re.findall(r'<img', html_content, re.IGNORECASE)),
            "link_count": len(re.findall(r'<a', html_content, re.IGNORECASE))
        }

        # 生成内容预览（前1000字符）
        analysis["content_preview"] = html_content[:1000] + "..." if len(html_content) > 1000 else html_content

        return analysis

    def extract_content(self, html_content: str, requirements: List[str], url: str, description: str = "",
                       document_metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """基于需求从HTML内容中智能提取信息"""

        # 优先使用来自HTML处理器的分析结果
        if document_metadata and 'html_analysis' in document_metadata:
            html_analysis = document_metadata['html_analysis']
            logger.info(f"使用HTML处理器的分析结果: 页面类型={html_analysis.get('page_type')}, "
                       f"检测到产品={html_analysis.get('has_products')}")
        else:
            # 如果没有预分析结果，进行本地分析
            html_analysis = self._analyze_html_content(html_content)
            logger.info(f"本地HTML内容分析: {html_analysis}")

        # 生成动态模式（传入URL和描述用于多项目检测）
        schema = self._generate_dynamic_schema(requirements, url, description)

        # 使用强力压缩方法大幅减少HTML内容大小
        compressed_html = self._compress_html_content(html_content, compression_level="aggressive")

        # 如果是多项目模式，尝试进一步提取商品片段
        if schema.get("is_multiple_items", False):
            extracted_html = self._extract_product_sections(compressed_html)
            final_html = extracted_html
        else:
            final_html = compressed_html

        # 根据最终处理后的大小决定使用的内容长度
        max_content_length = 12000 if schema.get("is_multiple_items", False) else 10000
        content_preview = final_html[:max_content_length] if len(final_html) > max_content_length else final_html

        logger.info(f"📊 HTML内容处理流程: 原始{len(html_content)} -> 压缩{len(compressed_html)} -> 提取{len(final_html)} -> 使用{len(content_preview)}")

        # 计算总体压缩率
        total_compression_ratio = (len(html_content) - len(content_preview)) / len(html_content) * 100
        logger.info(f"🎯 总体压缩率: {total_compression_ratio:.1f}% (多项目模式: {schema.get('is_multiple_items', False)})")

        # 如果最终内容仍然很大，记录警告
        if len(final_html) > max_content_length * 1.5:
            logger.warning(f"⚠️  最终HTML内容仍然较大: {len(final_html)} -> 使用{len(content_preview)}")
        else:
            logger.info(f"✅ HTML处理效果良好，内容大小适中")

        # 如果没有检测到产品相关内容，记录警告并提供更多信息
        if not html_analysis["has_products"]:
            logger.warning("⚠️  HTML内容中未检测到明显的产品相关模式，可能影响提取效果")
            logger.warning(f"HTML结构信息: {html_analysis['structure_info']}")
            logger.warning(f"HTML内容预览: {html_analysis['content_preview']}")

            # 检查是否是登录页面或错误页面
            login_indicators = ["login", "sign in", "password", "username", "authentication"]
            error_indicators = ["error", "not found", "access denied", "unauthorized"]

            content_lower = html_content.lower()
            if any(indicator in content_lower for indicator in login_indicators):
                logger.warning("🔐 页面可能是登录页面，请检查登录状态")
            if any(indicator in content_lower for indicator in error_indicators):
                logger.warning("❌ 页面可能显示错误信息，请检查URL和权限")
        else:
            logger.info(f"✅ 检测到产品相关模式: {[ind['pattern'] for ind in html_analysis['product_indicators']]}")

        # 保存HTML内容到文件用于调试（仅在调试模式下）
        if os.getenv('DEBUG_HTML', '').lower() == 'true':
            debug_filename = f"debug_html_{int(time.time())}.html"
            with open(debug_filename, 'w', encoding='utf-8') as f:
                f.write(html_content)
            logger.info(f"🔍 调试模式：HTML内容已保存到 {debug_filename}")

        # 使用优化的system prompt
        system_prompt = self._optimize_system_prompt(requirements, url, schema["is_multiple_items"])



        # 添加重试机制
        max_retries = 1
        retry_delay = 3  # 内容提取使用更长的延迟

        logger.info(f"网页数据提取 system prompt: {system_prompt}")
        for attempt in range(max_retries):
            try:
                logger.info(f"尝试第 {attempt + 1} 次内容提取...")

                # 在重试时添加延迟，内容提取需要更长间隔
                if attempt > 0:
                    logger.info(f"等待 {retry_delay} 秒后重试...")
                    time_module.sleep(retry_delay)

                # 在第一次内容提取前也添加短暂延迟，避免与意图分析调用冲突
                elif attempt == 0:
                    logger.info("添加API调用间隔，避免连接冲突...")
                    time_module.sleep(3)  # 增加到3秒

                # 记录请求详情
                total_request_size = len(system_prompt) + len(content_preview)
                logger.info(f"📊 API请求详情: prompt={len(system_prompt)}字符, HTML={len(content_preview)}字符, 总计={total_request_size}字符")

                # 检查请求大小，如果仍然过大则进一步缩减
                if total_request_size > 15000:  # 约15k字符限制
                    logger.warning(f"⚠️  请求仍然过大({total_request_size}字符)，进一步缩减HTML内容")
                    reduced_length = max(1000, 15000 - len(system_prompt) - 100)  # 保留至少1000字符
                    content_preview = content_preview[:reduced_length]
                    logger.info(f"📊 缩减后: HTML={len(content_preview)}字符")

                response = self.client.chat.completions.create(
                    model="gpt-4o-mini",  # 保持一致
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": f"HTML:\n{content_preview}"}
                    ],
                    temperature=0.1,
                    timeout=45  # 减少超时时间，快速失败
                )

                logger.info(f"成功获取AI响应，response: {response} ")
                result_text = response.choices[0].message.content
                logger.info(f"成功获取AI响应，长度: {len(result_text)} 字符")
                logger.info(f"成功获取AI响应，result_text: {result_text} ")

                try:
                    # 清理JSON中的注释
                    cleaned_result = self._clean_json_response(result_text)
                    result = json.loads(cleaned_result)
                    # 验证结果结构
                    if "extracted_data" not in result:
                        result = {"extracted_data": result, "summary": "数据提取完成", "confidence": 0.8}

                    # 验证多项目结果
                    if schema.get("is_multiple_items", False):
                        extracted_data = result.get("extracted_data", {})
                        items = extracted_data.get("items", [])

                        if not items:
                            logger.warning("⚠️  AI返回了空的商品列表，可能的原因：")
                            logger.warning("1. HTML内容中没有商品信息（登录失败、页面加载问题）")
                            logger.warning("2. AI无法识别当前页面的HTML结构")
                            logger.warning("3. 商品信息在HTML的后半部分，被内容长度限制截断")
                            logger.warning(f"HTML分析结果: {html_analysis}")

                            # 在结果中添加调试信息
                            result["debug_info"] = {
                                "html_analysis": html_analysis,
                                "content_length_used": len(content_preview),
                                "total_content_length": len(html_content),
                                "schema_detection": schema,
                                "possible_issues": [
                                    "HTML内容中没有商品信息",
                                    "AI无法识别HTML结构",
                                    "内容被截断",
                                    "登录失败或页面加载问题"
                                ]
                            }
                        else:
                            logger.info(f"✅ 成功提取 {len(items)} 个商品")

                    logger.info("成功解析AI响应为JSON格式")
                    return result
                except json.JSONDecodeError:
                    logger.error(f"Failed to parse extraction result: {result_text}")
                    return {
                        "extracted_data": {"raw_ai_response": result_text},
                        "summary": "AI响应解析失败",
                        "confidence": 0.0,
                        "notes": "无法解析AI返回的JSON格式"
                    }

            except Exception as e:
                logger.error(f"API调用失败栈信息: {e}")
                error_msg = str(e)
                logger.error(f"API调用失败详情: {error_msg}")

                # 检查具体错误类型
                if "model" in error_msg.lower():
                    logger.error("❌ 模型相关错误，可能是模型不可用或权限问题")
                elif "quota" in error_msg.lower() or "limit" in error_msg.lower():
                    logger.error("❌ API配额或限制问题")
                elif "connection" in error_msg.lower() or "timeout" in error_msg.lower():
                    logger.error("❌ 网络连接或超时问题")
                    # 对于连接问题，尝试进一步减少内容大小
                    if attempt < max_retries - 1:
                        logger.info("🔄 检测到连接问题，将在下次重试时进一步减少内容大小")
                        # 在下次重试时会进一步缩减内容
                elif "request" in error_msg.lower() and ("large" in error_msg.lower() or "size" in error_msg.lower()):
                    logger.error("❌ 请求过大错误，需要减少内容大小")

                # 检查是否是连接错误，需要重试
                if any(keyword in error_msg.lower() for keyword in ["connection", "timeout", "network", "read timeout", "connect timeout"]):
                    if attempt < max_retries - 1:
                        logger.info(f"检测到网络问题，{retry_delay} 秒后重试...")
                        time_module.sleep(retry_delay)
                        retry_delay *= 2  # 指数退避
                        continue

                # 如果是最后一次尝试或非网络错误，返回错误
                if attempt == max_retries - 1:
                    logger.error(f"所有重试均失败，最终错误: {error_msg}")
                    return {
                        "extracted_data": {},
                        "summary": f"内容提取失败: {error_msg}",
                        "confidence": 0.0,
                        "notes": f"AI内容提取过程中发生错误，已重试 {max_retries} 次。建议检查网络连接或减少内容大小。"
                    }

        # 理论上不会到达这里，但为了安全起见
        return {
            "extracted_data": {},
            "summary": "内容提取失败: 未知错误",
            "confidence": 0.0,
            "notes": "AI内容提取过程中发生未知错误"
        }


class IntelligentWebScraper:
    """智能网页爬虫主类"""
    
    def __init__(self, openai_api_key: str):
        if not openai or not OpenAI:
            raise ImportError("请安装 openai 库: pip install openai")
        
        self.openai_client = OpenAI(api_key=openai_api_key)
        self.intent_analyzer = IntentAnalyzer(self.openai_client)
        self.content_extractor = ContentExtractor(self.openai_client)
        
        # 配置爬虫
        self.loader_config = LoaderConfig(
            timeout=60,
            wait_for_js=3.0,
            retries=2,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        )
    
    async def scrape_from_natural_language(self, user_input: str) -> ScrapingResult:
        """基于自然语言输入进行智能爬取"""
        
        logger.info(f"分析用户输入: {user_input}")
        
        # 1. 分析用户意图
        intent = self.intent_analyzer.analyze_intent(user_input)
        
        if not intent.url:
            return ScrapingResult(
                success=False,
                url='',
                extracted_data={},
                raw_content='',
                metadata={'intent': intent.__dict__},
                error_message="无法从输入中提取有效的URL"
            )
        
        logger.info(f"提取的意图: URL={intent.url}, 需要登录={intent.needs_login}")
        
        # 2. 选择合适的加载器
        if intent.needs_login and intent.username and intent.password:
            loader = AuthenticatedHTMLLoader(self.loader_config)
            login_url = intent.login_url or intent.url
            
            try:
                documents = await loader.load_with_auth(
                    target_url=intent.url,
                    username=intent.username,
                    password=intent.password,
                    login_url=login_url
                )
            except Exception as e:
                return ScrapingResult(
                    success=False,
                    url=intent.url,
                    extracted_data={},
                    raw_content='',
                    metadata={'intent': intent.__dict__},
                    error_message=f"认证爬取失败: {str(e)}"
                )
        else:
            loader = SmartHTMLLoader(self.loader_config)
            try:
                documents = await asyncio.get_event_loop().run_in_executor(
                    None, loader.load, intent.url
                )
            except Exception as e:
                return ScrapingResult(
                    success=False,
                    url=intent.url,
                    extracted_data={},
                    raw_content='',
                    metadata={'intent': intent.__dict__},
                    error_message=f"普通爬取失败: {str(e)}"
                )
        
        if not documents:
            return ScrapingResult(
                success=False,
                url=intent.url,
                extracted_data={},
                raw_content='',
                metadata={'intent': intent.__dict__},
                error_message="未能获取网页内容"
            )
        
        document = documents[0]
        raw_content = document.page_content
        
        logger.info(f"成功获取内容，长度: {len(raw_content)} 字符")
        
        # 3. 智能内容提取
        if intent.content_requirements:
            extracted_data = self.content_extractor.extract_content(
                raw_content,
                intent.content_requirements,
                intent.url,
                user_input,  # 传入原始用户输入用于多项目检测
                document.metadata  # 传入文档元数据，包含HTML分析结果
            )
        else:
            extracted_data = {
                "raw_content": raw_content[:2000],
                "summary": "未指定具体提取要求，返回原始内容预览",
                "confidence": 1.0
            }
        
        return ScrapingResult(
            success=True,
            url=intent.url,
            extracted_data=extracted_data,
            raw_content=raw_content,
            metadata={
                'intent': intent.__dict__,
                'document_metadata': document.metadata,
                'timestamp': datetime.now().isoformat()
            }
        )
    
    def save_result(self, result: ScrapingResult, filename: Optional[str] = None) -> str:
        """保存爬取结果到文件"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"scraping_result_{timestamp}.json"
        
        result_dict = {
            "success": result.success,
            "url": result.url,
            "extracted_data": result.extracted_data,
            "raw_content_preview": result.raw_content[:1000] if result.raw_content else "",
            "raw_content_length": len(result.raw_content) if result.raw_content else 0,
            "metadata": result.metadata,
            "error_message": result.error_message
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(result_dict, f, indent=2, ensure_ascii=False)
        
        logger.info(f"结果已保存到: {filename}")
        return filename
