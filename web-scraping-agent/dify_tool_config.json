{"name": "web_scraping_agent", "label": {"en_US": "Web Scraping Agent", "zh_Hans": "智能网页爬虫"}, "description": {"en_US": "Intelligent web scraping agent that supports natural language input, automatic login detection, and smart content extraction", "zh_Hans": "智能网页爬虫代理，支持自然语言输入、自动登录检测和智能内容提取"}, "icon": "🕷️", "author": "Your Name", "version": "1.0.0", "type": "api", "api": {"method": "POST", "url": "http://your-server:8000/tools/web-scraping", "headers": {"Content-Type": "application/json"}}, "parameters": [{"name": "description", "label": {"en_US": "Scraping Description", "zh_Hans": "爬取需求描述"}, "type": "string", "required": true, "description": {"en_US": "Natural language description of what you want to scrape from the website", "zh_Hans": "用自然语言描述你想从网站爬取什么内容"}, "placeholder": {"en_US": "e.g., Extract product name, price and reviews from this e-commerce page", "zh_Hans": "例如：从这个电商页面提取商品名称、价格和评论"}}, {"name": "url", "label": {"en_US": "Target URL", "zh_Hans": "目标网址"}, "type": "string", "required": false, "description": {"en_US": "The URL of the webpage to scrape (optional if included in description)", "zh_Hans": "要爬取的网页URL（如果描述中已包含则可选）"}, "placeholder": {"en_US": "https://example.com/page", "zh_Hans": "https://example.com/page"}}, {"name": "username", "label": {"en_US": "Username", "zh_Hans": "用户名"}, "type": "string", "required": false, "description": {"en_US": "Username for login (if the website requires authentication)", "zh_Hans": "登录用户名（如果网站需要认证）"}, "placeholder": {"en_US": "your-username", "zh_Hans": "你的用户名"}}, {"name": "password", "label": {"en_US": "Password", "zh_Hans": "密码"}, "type": "string", "required": false, "description": {"en_US": "Password for login (if the website requires authentication)", "zh_Hans": "登录密码（如果网站需要认证）"}, "placeholder": {"en_US": "your-password", "zh_Hans": "你的密码"}, "sensitive": true}], "output": {"type": "object", "properties": {"status": {"type": "string", "description": "Scraping status (success/error)"}, "data": {"type": "object", "description": "Extracted content data"}, "summary": {"type": "string", "description": "Summary of the scraping result"}, "metadata": {"type": "object", "description": "Additional metadata about the scraping process"}}}, "examples": [{"input": {"description": "Extract the title and main content from this news article", "url": "https://news.example.com/article/123"}, "output": {"status": "success", "data": {"title": "Breaking News: Important Event Happened", "content": "The main content of the news article...", "author": "<PERSON>", "publish_date": "2024-01-20"}, "summary": "Successfully extracted news article content", "metadata": {"url": "https://news.example.com/article/123", "confidence": 0.95, "content_length": 1500}}}, {"input": {"description": "Get product information including name, price, and stock status", "url": "https://shop.example.com/product/456"}, "output": {"status": "success", "data": {"product_name": "iPhone 15 Pro", "price": "$999.00", "stock_status": "In Stock", "rating": "4.8/5", "reviews_count": "1,234"}, "summary": "Successfully extracted product information", "metadata": {"url": "https://shop.example.com/product/456", "confidence": 0.92, "content_length": 2800}}}, {"input": {"description": "Scrape my order history from the member area", "url": "https://shop.example.com/orders", "username": "<EMAIL>", "password": "mypassword"}, "output": {"status": "success", "data": {"orders": [{"order_id": "12345", "date": "2024-01-15", "total": "$299.99", "status": "Delivered"}, {"order_id": "12346", "date": "2024-01-10", "total": "$149.99", "status": "Shipped"}]}, "summary": "Successfully extracted order history", "metadata": {"url": "https://shop.example.com/orders", "confidence": 0.88, "authenticated": true}}}], "tags": ["web-scraping", "data-extraction", "automation", "ai-powered"], "categories": ["data-processing", "web-automation"]}