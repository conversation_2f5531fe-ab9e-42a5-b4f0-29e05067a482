#!/usr/bin/env python3
"""
Web Scraping Agent

将智能爬虫系统封装成标准的 Agent，支持 Dify 集成
"""

import asyncio
import json
import logging
import threading
from typing import Dict, Any, Optional, List
from datetime import datetime
from dataclasses import dataclass, asdict

from intelligent_web_scraper import IntelligentWebScraper, ScrapingResult


@dataclass
class AgentResponse:
    """Agent 响应数据结构"""
    success: bool
    data: Dict[str, Any]
    message: str
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class WebScrapingAgent:
    """网页爬取 Agent"""
    
    def __init__(self, openai_api_key: str, config: Optional[Dict] = None):
        """
        初始化 Web Scraping Agent
        
        Args:
            openai_api_key: OpenAI API Key
            config: 可选配置参数
        """
        self.scraper = IntelligentWebScraper(openai_api_key)
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 配置日志
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def scrape_website(
        self, 
        description: str,
        url: Optional[str] = None,
        username: Optional[str] = None,
        password: Optional[str] = None,
        content_requirements: Optional[List[str]] = None
    ) -> AgentResponse:
        """
        网页爬取主方法 - 同步版本（适用于 Dify）
        
        Args:
            description: 自然语言描述的爬取需求
            url: 可选的目标URL（如果description中没有包含）
            username: 可选的登录用户名
            password: 可选的登录密码
            content_requirements: 可选的具体内容要求列表
            
        Returns:
            AgentResponse: 标准化的响应结果
        """
        try:
            # 构建完整的用户输入
            user_input = self._build_user_input(
                description, url, username, password, content_requirements
            )
            
            self.logger.info(f"开始处理爬取请求: {description}")
            
            # 执行异步爬取
            result = self._run_async_scraping(user_input)
            
            return self._format_response(result)
            
        except Exception as e:
            self.logger.error(f"爬取过程中发生错误: {e}")
            return AgentResponse(
                success=False,
                data={},
                message="爬取失败",
                error=str(e),
                metadata={"timestamp": datetime.now().isoformat()}
            )
    
    async def scrape_website_async(
        self,
        description: str,
        url: Optional[str] = None,
        username: Optional[str] = None,
        password: Optional[str] = None,
        content_requirements: Optional[List[str]] = None
    ) -> AgentResponse:
        """
        网页爬取主方法 - 异步版本
        """
        try:
            user_input = self._build_user_input(
                description, url, username, password, content_requirements
            )
            
            self.logger.info(f"开始处理异步爬取请求: {description}")
            
            result = await self.scraper.scrape_from_natural_language(user_input)
            return self._format_response(result)
            
        except Exception as e:
            self.logger.error(f"异步爬取过程中发生错误: {e}")
            return AgentResponse(
                success=False,
                data={},
                message="爬取失败",
                error=str(e),
                metadata={"timestamp": datetime.now().isoformat()}
            )
    
    def _build_user_input(
        self,
        description: str,
        url: Optional[str] = None,
        username: Optional[str] = None,
        password: Optional[str] = None,
        content_requirements: Optional[List[str]] = None
    ) -> str:
        """构建完整的用户输入"""
        user_input = description
        
        # 添加URL（如果提供且description中没有包含）
        if url and "http" not in description.lower():
            user_input += f"，网址是{url}"
        
        # 添加登录信息
        if username and password:
            user_input += f"，账号是{username}，密码是{password}"
        
        # 添加具体内容要求
        if content_requirements:
            requirements_text = "、".join(content_requirements)
            if "提取" not in user_input and "爬取" not in user_input:
                user_input += f"，需要提取{requirements_text}"
            elif requirements_text not in user_input:
                user_input += f"，特别是{requirements_text}"
        
        return user_input

    def _run_async_scraping(self, user_input: str) -> ScrapingResult:
        """在新线程中运行异步爬取，避免事件循环冲突"""
        def run_in_thread():
            # 在新线程中创建新的事件循环
            new_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(new_loop)
            try:
                return new_loop.run_until_complete(
                    self.scraper.scrape_from_natural_language(user_input)
                )
            finally:
                new_loop.close()

        # 在新线程中运行
        import concurrent.futures
        with concurrent.futures.ThreadPoolExecutor() as executor:
            future = executor.submit(run_in_thread)
            return future.result()

    def _format_response(self, result: ScrapingResult) -> AgentResponse:
        """格式化响应结果"""
        if result.success:
            # 成功情况
            extracted_data = result.extracted_data.get('extracted_data', {})
            summary = result.extracted_data.get('summary', '爬取完成')
            confidence = result.extracted_data.get('confidence', 0)
            notes = result.extracted_data.get('notes', '')

            # 构建结构化的响应数据
            response_data = {
                "url": result.url,
                "extracted_content": extracted_data,
                "summary": summary,
                "confidence": confidence,
                "notes": notes,
                "content_length": len(result.raw_content) if result.raw_content else 0,
                "extraction_metadata": {
                    "total_fields": len(extracted_data) if isinstance(extracted_data, dict) else 0,
                    "successful_extractions": sum(1 for v in extracted_data.values() if v is not None) if isinstance(extracted_data, dict) else 0,
                    "failed_extractions": sum(1 for v in extracted_data.values() if v is None) if isinstance(extracted_data, dict) else 0
                }
            }

            # 检测是否为多项目结构
            is_multiple_items = isinstance(extracted_data, dict) and "items" in extracted_data

            if is_multiple_items:
                # 多项目统计
                items = extracted_data.get("items", [])
                response_data["extraction_metadata"]["item_count"] = len(items)
                response_data["extraction_metadata"]["structure_type"] = "multiple_items"

                # 分析每个项目的字段完整性
                if items:
                    first_item = items[0] if items else {}
                    total_fields_per_item = len(first_item)

                    item_stats = []
                    for i, item in enumerate(items):
                        if isinstance(item, dict):
                            successful_fields = sum(1 for v in item.values() if v is not None)
                            item_stats.append({
                                "item_index": i,
                                "total_fields": len(item),
                                "successful_fields": successful_fields,
                                "completeness": successful_fields / max(len(item), 1)
                            })

                    response_data["item_statistics"] = item_stats
                    response_data["extraction_metadata"]["average_completeness"] = sum(stat["completeness"] for stat in item_stats) / len(item_stats) if item_stats else 0

            else:
                # 单项目统计（原有逻辑）
                response_data["extraction_metadata"]["structure_type"] = "single_item"

                if isinstance(extracted_data, dict) and extracted_data:
                    field_stats = {}
                    for field_name, field_value in extracted_data.items():
                        if field_value is not None:
                            if isinstance(field_value, list):
                                field_stats[field_name] = {"type": "array", "count": len(field_value)}
                            elif isinstance(field_value, dict):
                                field_stats[field_name] = {"type": "object", "keys": len(field_value)}
                            elif isinstance(field_value, (int, float)):
                                field_stats[field_name] = {"type": "number", "value": field_value}
                            elif isinstance(field_value, bool):
                                field_stats[field_name] = {"type": "boolean", "value": field_value}
                            else:
                                field_stats[field_name] = {"type": "string", "length": len(str(field_value))}
                        else:
                            field_stats[field_name] = {"type": "null", "reason": "not_found"}

                    response_data["field_statistics"] = field_stats

            return AgentResponse(
                success=True,
                data=response_data,
                message=f"成功爬取网页内容: {summary}",
                metadata={
                    "timestamp": datetime.now().isoformat(),
                    "url": result.url,
                    "confidence": confidence,
                    "notes": notes,
                    "intent_metadata": result.metadata.get('intent', {}),
                    "extraction_quality": {
                        "confidence_level": "high" if confidence >= 0.8 else "medium" if confidence >= 0.5 else "low",
                        "data_completeness": response_data["extraction_metadata"]["successful_extractions"] / max(response_data["extraction_metadata"]["total_fields"], 1) if response_data["extraction_metadata"]["total_fields"] > 0 else 0
                    }
                }
            )
        else:
            # 失败情况
            return AgentResponse(
                success=False,
                data={"url": result.url},
                message="网页爬取失败",
                error=result.error_message,
                metadata={
                    "timestamp": datetime.now().isoformat(),
                    "url": result.url,
                    "intent_metadata": result.metadata.get('intent', {})
                }
            )
    
    def get_capabilities(self) -> Dict[str, Any]:
        """获取 Agent 能力描述"""
        return {
            "name": "Web Scraping Agent",
            "description": "智能网页爬取代理，支持自然语言输入、自动登录、智能内容提取",
            "version": "1.0.0",
            "capabilities": [
                "自然语言理解爬取需求",
                "自动检测登录需求",
                "智能内容提取",
                "结构化数据输出",
                "支持多种网站类型",
                "批量处理能力"
            ],
            "supported_content_types": [
                "文本内容（标题、正文、描述）",
                "商品信息（价格、库存、评论）",
                "表格数据",
                "图片链接",
                "联系信息",
                "列表数据"
            ],
            "input_parameters": {
                "description": {
                    "type": "string",
                    "required": True,
                    "description": "自然语言描述的爬取需求"
                },
                "url": {
                    "type": "string",
                    "required": False,
                    "description": "目标网页URL（可选，如果description中已包含）"
                },
                "username": {
                    "type": "string",
                    "required": False,
                    "description": "登录用户名（如果需要登录）"
                },
                "password": {
                    "type": "string",
                    "required": False,
                    "description": "登录密码（如果需要登录）"
                },
                "content_requirements": {
                    "type": "array",
                    "required": False,
                    "description": "具体的内容提取要求列表"
                }
            },
            "output_format": {
                "success": "boolean - 是否成功",
                "data": "object - 提取的数据",
                "message": "string - 结果描述",
                "error": "string - 错误信息（如果失败）",
                "metadata": "object - 元数据信息"
            }
        }
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 简单的健康检查
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "version": "1.0.0",
                "components": {
                    "scraper": "ok",
                    "openai": "ok" if self.scraper.openai_client else "error"
                }
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }


# 便捷函数
def create_web_scraping_agent(openai_api_key: str, config: Optional[Dict] = None) -> WebScrapingAgent:
    """创建 Web Scraping Agent 实例"""
    return WebScrapingAgent(openai_api_key, config)


# 示例使用
if __name__ == "__main__":
    import os
    
    # 获取 API Key
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("请设置 OPENAI_API_KEY 环境变量")
        exit(1)
    
    # 创建 Agent
    agent = create_web_scraping_agent(api_key)
    
    # 测试爬取
    response = agent.scrape_website(
        description="爬取百度首页的标题和搜索框信息",
        url="https://www.baidu.com"
    )
    
    print("Agent 响应:")
    print(json.dumps(asdict(response), indent=2, ensure_ascii=False))
