#!/usr/bin/env python3
"""
调试空结果问题

专门用于调试为什么AI返回空的商品列表
"""

import os
import json
import asyncio
from intelligent_web_scraper import IntelligentWebScraper, ContentExtractor
from openai import OpenAI


def test_html_analysis():
    """测试HTML内容分析功能"""
    
    print("=" * 80)
    print("🔍 HTML内容分析测试")
    print("=" * 80)
    
    # 模拟包含产品的HTML
    sample_html_with_products = """
    <html>
    <body>
        <div class="product-list">
            <div class="product-item" data-product-id="123">
                <h3 class="product-title">KINDER JOY T20 CP 20CT</h3>
                <img src="/images/product1.jpg" alt="Product 1">
                <span class="price">$18.49</span>
                <span class="upc">980057351</span>
                <span class="item-number">271244</span>
            </div>
            <div class="product-item" data-product-id="124">
                <h3 class="product-title">Another Product</h3>
                <img src="/images/product2.jpg" alt="Product 2">
                <span class="price">$19.99</span>
                <span class="upc">980057352</span>
                <span class="item-number">271245</span>
            </div>
        </div>
    </body>
    </html>
    """
    
    # 模拟不包含产品的HTML
    sample_html_without_products = """
    <html>
    <body>
        <div class="header">
            <h1>Welcome to our store</h1>
        </div>
        <div class="content">
            <p>Please log in to view products</p>
        </div>
    </body>
    </html>
    """
    
    try:
        client = OpenAI(api_key='demo-key')
        extractor = ContentExtractor(client)
    except:
        print("注意：这是演示模式")
        return
    
    print("🔸 测试包含产品的HTML:")
    analysis1 = extractor._analyze_html_content(sample_html_with_products)
    print(f"检测到产品: {analysis1['has_products']}")
    print(f"产品指标: {len(analysis1['product_indicators'])} 个")
    for indicator in analysis1['product_indicators']:
        print(f"  - {indicator['pattern']}: {indicator['count']} 个匹配")
    
    print("\n🔸 测试不包含产品的HTML:")
    analysis2 = extractor._analyze_html_content(sample_html_without_products)
    print(f"检测到产品: {analysis2['has_products']}")
    print(f"产品指标: {len(analysis2['product_indicators'])} 个")


def test_content_extraction_with_debug():
    """测试带调试信息的内容提取"""
    
    print("\n" + "=" * 80)
    print("🧪 内容提取调试测试")
    print("=" * 80)
    
    # 模拟真实的产品页面HTML
    realistic_html = """
    <!DOCTYPE html>
    <html>
    <head><title>Restaurant Depot Products</title></head>
    <body>
        <div class="container">
            <div class="product-grid">
                <div class="product-card" data-product="271244">
                    <div class="product-image">
                        <img src="/images/kinder-joy.jpg" alt="KINDER JOY T20 CP 20CT">
                    </div>
                    <div class="product-info">
                        <h4 class="product-title">KINDER JOY T20 CP 20CT</h4>
                        <div class="product-details">
                            <span class="item-number">Item #: 271244</span>
                            <span class="upc">UPC: 980057351</span>
                            <span class="bin">BIN: 1300</span>
                        </div>
                        <div class="price-info">
                            <span class="price">$18.49</span>
                        </div>
                    </div>
                </div>
                <div class="product-card" data-product="271245">
                    <div class="product-image">
                        <img src="/images/snickers.jpg" alt="SNICKERS BAR 48CT">
                    </div>
                    <div class="product-info">
                        <h4 class="product-title">SNICKERS BAR 48CT</h4>
                        <div class="product-details">
                            <span class="item-number">Item #: 271245</span>
                            <span class="upc">UPC: 980057352</span>
                            <span class="bin">BIN: 1301</span>
                        </div>
                        <div class="price-info">
                            <span class="price">$24.99</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    """
    
    try:
        client = OpenAI(api_key='demo-key')
        extractor = ContentExtractor(client)
    except:
        print("注意：这是演示模式，无法实际调用AI")
        return
    
    requirements = ["title", "image", "item Number", "upc", "BIN", "price"]
    url = "https://member.restaurantdepot.com/products"
    description = "爬取商品页面数据"
    
    print("🔸 分析HTML内容...")
    analysis = extractor._analyze_html_content(realistic_html)
    print(f"HTML分析结果: {json.dumps(analysis, indent=2, ensure_ascii=False)}")
    
    print("\n🔸 检测多项目模式...")
    schema = extractor._generate_dynamic_schema(requirements, url, description)
    print(f"多项目检测: {schema['is_multiple_items']}")
    
    print("\n🔸 模拟AI提取过程...")
    print("如果AI返回空结果，可能的原因：")
    print("1. OpenAI API密钥无效或网络问题")
    print("2. AI模型无法理解HTML结构")
    print("3. 提示词不够明确")
    print("4. HTML内容被过度截断")


def show_debugging_guide():
    """显示调试指南"""
    
    print("\n" + "=" * 80)
    print("🔧 空结果调试指南")
    print("=" * 80)
    
    print("当AI返回空的商品列表时，按以下步骤调试：")
    
    print("\n1. 🔍 检查HTML内容分析")
    print("   - 查看日志中的 'HTML内容分析' 信息")
    print("   - 确认 'has_products' 为 true")
    print("   - 检查检测到的产品指标数量")
    
    print("\n2. 📏 检查内容长度")
    print("   - 查看 'HTML内容长度' 和 '使用长度'")
    print("   - 确认多项目模式使用20000字符限制")
    print("   - 如果原始内容很长，可能需要增加限制")
    
    print("\n3. 🌐 检查登录状态")
    print("   - 确认用户名密码正确")
    print("   - 检查是否成功登录到会员页面")
    print("   - 验证页面是否显示产品而不是登录提示")
    
    print("\n4. 🎯 检查AI提示")
    print("   - 查看生成的AI提示是否包含HTML解析指南")
    print("   - 确认提示中包含强制要求提取所有商品")
    print("   - 检查是否包含HTML分析结果")
    
    print("\n5. 🔄 检查API调用")
    print("   - 确认OpenAI API密钥有效")
    print("   - 检查网络连接稳定性")
    print("   - 查看是否有API调用错误")
    
    print("\n6. 📋 查看调试信息")
    print("   - 如果返回空结果，会包含 debug_info 字段")
    print("   - 查看 possible_issues 列表")
    print("   - 根据具体问题采取相应措施")


def show_solution_recommendations():
    """显示解决方案建议"""
    
    print("\n" + "=" * 80)
    print("💡 解决方案建议")
    print("=" * 80)
    
    print("根据不同的问题类型，推荐以下解决方案：")
    
    print("\n🔸 HTML内容问题:")
    print("   - 增加等待时间，让页面完全加载")
    print("   - 检查登录流程，确保成功进入会员页面")
    print("   - 尝试不同的URL或页面参数")
    
    print("\n🔸 内容长度问题:")
    print("   - 增加多项目页面的字符限制（当前20000）")
    print("   - 优化HTML预处理，移除无关内容")
    print("   - 分段处理大型页面")
    
    print("\n🔸 AI解析问题:")
    print("   - 进一步优化AI提示词")
    print("   - 提供更多HTML结构示例")
    print("   - 尝试不同的AI模型（gpt-4 vs gpt-4o-mini）")
    
    print("\n🔸 网络/API问题:")
    print("   - 检查网络连接和防火墙设置")
    print("   - 验证OpenAI API密钥和配额")
    print("   - 增加重试次数和超时时间")


if __name__ == "__main__":
    print("🚀 开始空结果调试分析...")
    
    test_html_analysis()
    test_content_extraction_with_debug()
    show_debugging_guide()
    show_solution_recommendations()
    
    print("\n" + "=" * 80)
    print("✅ 调试分析完成！")
    print("=" * 80)
    print("下一步：")
    print("1. 运行实际的爬虫测试")
    print("2. 查看详细的日志输出")
    print("3. 根据调试信息确定具体问题")
    print("4. 应用相应的解决方案")
