#!/usr/bin/env python3
"""
修复OpenAI API连接问题

专门解决第二次API调用时的连接错误问题
"""

import os
import time
import json
import asyncio
from intelligent_web_scraper import IntelligentWebScraper


def test_api_connection():
    """测试API连接稳定性"""
    
    print("=" * 80)
    print("🔗 OpenAI API连接测试")
    print("=" * 80)
    
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ 请设置环境变量: export OPENAI_API_KEY='your-key'")
        return False
    
    try:
        from openai import OpenAI
        client = OpenAI(api_key=api_key)
        
        print("🔸 测试简单API调用...")
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": "Hello"}],
            timeout=30
        )
        print("✅ 基础API调用成功")
        
        # 测试连续调用
        print("\n🔸 测试连续API调用...")
        for i in range(3):
            print(f"   调用 {i+1}/3...")
            response = client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": f"Test {i+1}"}],
                timeout=30
            )
            time.sleep(2)  # 添加间隔
        print("✅ 连续API调用成功")
        
        return True
        
    except Exception as e:
        print(f"❌ API连接测试失败: {e}")
        return False


async def test_scraper_with_delays():
    """测试带延迟的爬虫调用"""
    
    print("\n" + "=" * 80)
    print("🕐 带延迟的爬虫测试")
    print("=" * 80)
    
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ 请设置API密钥")
        return False
    
    scraper = IntelligentWebScraper(api_key)
    
    # 简化的测试用例
    user_input = """请帮我爬取这个页面的商品数据
URL: https://member.restaurantdepot.com/products?id=5&sort=saleranking&it=product&category=1%7cCandy%20%26%20Snacks
username：<EMAIL>
password：Jetsons823
content_requirements：title, price"""
    
    print("📝 执行简化的爬取测试...")
    
    try:
        result = await scraper.scrape_from_natural_language(user_input)
        
        if result.success:
            print("✅ 爬取成功，连接问题已解决！")
            
            # 检查提取结果
            extracted_data = result.extracted_data
            if "extracted_data" in extracted_data and "items" in extracted_data["extracted_data"]:
                items = extracted_data["extracted_data"]["items"]
                print(f"🛍️  提取的商品数量: {len(items)}")
            
            return True
        else:
            print(f"❌ 爬取失败: {result.error_message}")
            return False
            
    except Exception as e:
        print(f"💥 测试异常: {e}")
        return False


def show_connection_tips():
    """显示连接问题解决建议"""
    
    print("\n" + "=" * 80)
    print("💡 连接问题解决建议")
    print("=" * 80)
    
    print("已实施的修复:")
    print("✅ 1. 增加API调用间隔（2-3秒）")
    print("✅ 2. 增加超时时间（90秒）")
    print("✅ 3. 简化HTML分析结果传递")
    print("✅ 4. 改进重试机制")
    print("✅ 5. 修正模型名称")
    
    print("\n如果仍有连接问题，可以尝试:")
    print("🔸 检查网络连接稳定性")
    print("🔸 验证OpenAI API密钥有效性")
    print("🔸 检查API配额和限制")
    print("🔸 尝试使用VPN或更换网络")
    print("🔸 增加更长的调用间隔")
    
    print("\n环境变量设置:")
    print("export OPENAI_API_KEY='your-api-key-here'")
    print("export DEBUG_HTML=true  # 可选，用于调试")


def show_optimization_summary():
    """显示优化总结"""
    
    print("\n" + "=" * 80)
    print("🚀 连接优化总结")
    print("=" * 80)
    
    print("问题原因分析:")
    print("• 第二次API调用包含大量HTML内容")
    print("• 连续快速调用导致连接冲突")
    print("• 超时时间不足以处理复杂请求")
    print("• 请求体积过大影响网络传输")
    
    print("\n解决方案:")
    print("• ⏱️  添加API调用间隔（避免冲突）")
    print("• 🕐 增加超时时间（90秒）")
    print("• 📦 简化HTML分析结果（减少体积）")
    print("• 🔄 改进重试机制（更智能的延迟）")
    print("• 🎯 优化prompt内容（去除冗余）")
    
    print("\n性能改进:")
    print("• 减少网络传输数据量")
    print("• 提高连接稳定性")
    print("• 更好的错误恢复能力")
    print("• 更清晰的调试信息")


async def main():
    """主函数"""
    
    print("🔧 OpenAI API连接问题修复工具")
    print("=" * 80)
    
    # 测试基础连接
    connection_ok = test_api_connection()
    
    if connection_ok:
        # 测试爬虫功能
        scraper_ok = await test_scraper_with_delays()
        
        if scraper_ok:
            print("\n🎉 所有测试通过！连接问题已解决。")
        else:
            print("\n🔧 爬虫测试失败，需要进一步调试。")
    else:
        print("\n❌ 基础API连接失败，请检查网络和密钥。")
    
    show_connection_tips()
    show_optimization_summary()
    
    print("\n" + "=" * 80)
    print("✅ 连接问题修复完成")
    print("=" * 80)


if __name__ == "__main__":
    asyncio.run(main())
