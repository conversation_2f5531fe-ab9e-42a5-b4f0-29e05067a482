#!/usr/bin/env python3
"""
修复time模块导入问题

解决 'local variable 'time' referenced before assignment' 错误
"""

import os
import re


def find_time_conflicts():
    """查找可能的time变量冲突"""
    
    print("🔍 查找time变量冲突...")
    
    file_path = "intelligent_web_scraper.py"
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
        lines = content.split('\n')
    
    # 查找可能的time变量定义
    time_patterns = [
        r'\btime\s*=',  # time = something
        r'def\s+\w*time\w*',  # 函数名包含time
        r'for\s+time\s+in',  # for time in ...
        r'time\s*,',  # time作为元组元素
        r'time\s*:',  # time作为字典键或类型注解
    ]
    
    conflicts = []
    for i, line in enumerate(lines, 1):
        for pattern in time_patterns:
            if re.search(pattern, line, re.IGNORECASE):
                conflicts.append((i, line.strip(), pattern))
    
    if conflicts:
        print("❌ 发现可能的time变量冲突:")
        for line_num, line_content, pattern in conflicts:
            print(f"   行 {line_num}: {line_content}")
            print(f"   模式: {pattern}")
    else:
        print("✅ 未发现明显的time变量冲突")
    
    return conflicts


def fix_time_import():
    """修复time导入问题"""
    
    print("\n🔧 修复time导入问题...")
    
    file_path = "intelligent_web_scraper.py"
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 确保在文件顶部有正确的time导入
    import_section = """import asyncio
import json
import logging
import os
import re
import time
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from dataclasses import dataclass"""
    
    # 查找当前的导入部分
    lines = content.split('\n')
    import_end = 0
    
    for i, line in enumerate(lines):
        if line.startswith('import ') or line.startswith('from '):
            import_end = i
        elif line.strip() == '' and import_end > 0:
            continue
        elif import_end > 0:
            break
    
    # 检查是否已经有time导入
    has_time_import = any('import time' in line for line in lines[:import_end+5])
    
    if has_time_import:
        print("✅ time模块已正确导入")
    else:
        print("❌ 缺少time模块导入，正在修复...")
        # 在导入部分添加time
        for i, line in enumerate(lines):
            if line.startswith('import re'):
                lines.insert(i+1, 'import time')
                break
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        print("✅ 已添加time模块导入")


def create_test_script():
    """创建测试脚本"""
    
    print("\n📝 创建time导入测试脚本...")
    
    test_content = '''#!/usr/bin/env python3
"""
测试time模块导入

验证time.sleep()是否正常工作
"""

def test_time_import():
    """测试time模块导入"""
    
    print("🔍 测试time模块导入...")
    
    try:
        import time
        print("✅ time模块导入成功")
        
        print("🕐 测试time.sleep()...")
        time.sleep(0.1)
        print("✅ time.sleep()工作正常")
        
        return True
        
    except Exception as e:
        print(f"❌ time模块测试失败: {e}")
        return False


def test_in_function_scope():
    """在函数作用域内测试time"""
    
    print("\\n🔍 在函数作用域内测试time...")
    
    try:
        # 模拟ContentExtractor中的使用方式
        retry_delay = 2
        
        print("测试time.sleep()调用...")
        import time  # 局部导入测试
        time.sleep(0.1)
        print("✅ 函数作用域内time.sleep()正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 函数作用域内time测试失败: {e}")
        return False


if __name__ == "__main__":
    print("🧪 Time模块导入测试")
    print("=" * 50)
    
    success1 = test_time_import()
    success2 = test_in_function_scope()
    
    if success1 and success2:
        print("\\n🎉 所有time模块测试通过！")
    else:
        print("\\n❌ time模块测试失败，需要进一步调试")
'''
    
    with open('test_time_import.py', 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    print("✅ 测试脚本已创建: test_time_import.py")


def show_debugging_tips():
    """显示调试建议"""
    
    print("\n💡 调试建议:")
    print("1. 运行 python test_time_import.py 测试time模块")
    print("2. 检查是否有局部变量覆盖了time模块")
    print("3. 确认Python环境正常")
    print("4. 查看完整的错误堆栈信息")
    
    print("\n🔧 可能的解决方案:")
    print("• 使用完全限定的模块名: import time as time_module")
    print("• 在函数开始处重新导入: import time")
    print("• 检查是否有time变量名冲突")
    print("• 重启Python解释器清除缓存")


def main():
    """主函数"""
    
    print("🔧 Time模块导入问题修复工具")
    print("=" * 50)
    
    # 查找冲突
    conflicts = find_time_conflicts()
    
    # 修复导入
    fix_time_import()
    
    # 创建测试脚本
    create_test_script()
    
    # 显示建议
    show_debugging_tips()
    
    print("\n✅ 修复完成！")
    print("请运行: python test_time_import.py")


if __name__ == "__main__":
    main()
