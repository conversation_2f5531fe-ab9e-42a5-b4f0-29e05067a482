#!/usr/bin/env python3
"""
Web Scraping Agent 包验证脚本

验证独立包的完整性和功能
"""

import sys
import os
import importlib
from typing import List, <PERSON><PERSON>


def test_import(module_name: str, description: str) -> Tuple[bool, str]:
    """测试模块导入"""
    try:
        importlib.import_module(module_name)
        return True, f"✅ {description} - 导入成功"
    except ImportError as e:
        return False, f"❌ {description} - 导入失败: {e}"
    except Exception as e:
        return False, f"❌ {description} - 错误: {e}"


def test_class_creation(module_name: str, class_name: str, description: str) -> Tuple[bool, str]:
    """测试类创建"""
    try:
        module = importlib.import_module(module_name)
        cls = getattr(module, class_name)
        
        # 特殊处理需要参数的类
        if class_name in ['WebScrapingAgent', 'IntelligentWebScraper']:
            # 这些类需要 OpenAI API Key，我们只测试类是否存在
            return True, f"✅ {description} - 类定义正确"
        elif class_name == 'Document':
            # Document 类需要 page_content 参数
            instance = cls("test content")
            return True, f"✅ {description} - 实例创建成功"
        else:
            # 尝试创建实例
            instance = cls()
            return True, f"✅ {description} - 实例创建成功"
            
    except Exception as e:
        return False, f"❌ {description} - 创建失败: {e}"


def check_file_exists(filepath: str, description: str) -> Tuple[bool, str]:
    """检查文件是否存在"""
    if os.path.exists(filepath):
        return True, f"✅ {description} - 文件存在"
    else:
        return False, f"❌ {description} - 文件不存在"


def main():
    """主验证函数"""
    print("🤖 Web Scraping Agent 包验证")
    print("=" * 50)
    
    # 测试项目列表
    tests = [
        # 核心模块导入测试
        ("langchain_html_processor", "嵌入式 HTML 处理器"),
        ("langchain_html_processor.core", "HTML 处理器核心模块"),
        ("langchain_html_processor.core.document", "文档类模块"),
        ("langchain_html_processor.core.config", "配置类模块"),
        ("langchain_html_processor.core.html_loaders", "HTML 加载器模块"),
        
        # 主要功能模块
        ("intelligent_web_scraper", "智能网页爬虫"),
        ("web_scraping_agent", "Web Scraping Agent"),
        
        # API 服务模块
        ("dify_api_server", "Dify API 服务器"),
    ]
    
    # 类创建测试
    class_tests = [
        ("langchain_html_processor.core.document", "Document", "Document 类"),
        ("langchain_html_processor.core.config", "LoaderConfig", "LoaderConfig 类"),
        ("langchain_html_processor.core.html_loaders", "SmartHTMLLoader", "SmartHTMLLoader 类"),
        ("langchain_html_processor.core.html_loaders", "AuthenticatedHTMLLoader", "AuthenticatedHTMLLoader 类"),
        ("intelligent_web_scraper", "IntelligentWebScraper", "IntelligentWebScraper 类"),
        ("web_scraping_agent", "WebScrapingAgent", "WebScrapingAgent 类"),
    ]
    
    # 文件存在性测试
    file_tests = [
        ("requirements.txt", "依赖文件"),
        ("Dockerfile", "Docker 镜像文件"),
        ("docker-compose.yml", "Docker 编排文件"),
        (".env.example", "环境配置示例"),
        ("dify_tool_config.json", "Dify 工具配置"),
        ("DIFY_INTEGRATION_GUIDE.md", "Dify 集成指南"),
        ("COMPLETE_PACKAGE_GUIDE.md", "完整包指南"),
    ]
    
    # 执行测试
    all_passed = True
    
    print("\n📦 模块导入测试:")
    print("-" * 30)
    for module_name, description in tests:
        success, message = test_import(module_name, description)
        print(message)
        if not success:
            all_passed = False
    
    print("\n🏗️  类创建测试:")
    print("-" * 30)
    for module_name, class_name, description in class_tests:
        success, message = test_class_creation(module_name, class_name, description)
        print(message)
        if not success:
            all_passed = False
    
    print("\n📄 文件存在性测试:")
    print("-" * 30)
    for filepath, description in file_tests:
        success, message = check_file_exists(filepath, description)
        print(message)
        if not success:
            all_passed = False
    
    # 功能测试
    print("\n🔧 功能测试:")
    print("-" * 30)
    
    try:
        from langchain_html_processor import LoaderConfig, SmartHTMLLoader
        config = LoaderConfig(timeout=30)
        loader = SmartHTMLLoader(config)
        print("✅ 配置和加载器创建 - 成功")
    except Exception as e:
        print(f"❌ 配置和加载器创建 - 失败: {e}")
        all_passed = False
    
    try:
        from intelligent_web_scraper import ScrapingIntent, ScrapingResult
        intent = ScrapingIntent(url="https://example.com", content_requirements=["title"], needs_login=False)
        print("✅ 数据结构创建 - 成功")
    except Exception as e:
        print(f"❌ 数据结构创建 - 失败: {e}")
        all_passed = False
    
    # 总结
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有测试通过！包验证成功！")
        print("\n✅ 包状态:")
        print("  • 所有核心模块可正常导入")
        print("  • 所有类定义正确")
        print("  • 所有必需文件存在")
        print("  • 基本功能正常")
        
        print("\n🚀 下一步:")
        print("  1. 设置环境变量: cp .env.example .env")
        print("  2. 编辑 .env 文件，设置 OPENAI_API_KEY")
        print("  3. 启动服务: python start_agent_server.py")
        print("  4. 或使用 Docker: docker-compose up -d")
        
        return 0
    else:
        print("❌ 部分测试失败！请检查包完整性。")
        print("\n🔧 可能的解决方案:")
        print("  1. 检查所有文件是否完整")
        print("  2. 安装缺失的依赖: pip install -r requirements.txt")
        print("  3. 检查 Python 版本兼容性")
        
        return 1


if __name__ == "__main__":
    sys.exit(main())
