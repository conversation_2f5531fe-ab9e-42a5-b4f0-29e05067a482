# 🔧 Dify 自定义工具配置指南

## 📋 在 Dify 中创建 Web Scraping Agent 工具

### 步骤 1: 准备服务器

确保你的 Web Scraping Agent 服务正在运行：

```bash
cd web-scraping-agent
python start_agent_server.py
# 或使用 Docker
docker-compose up -d
```

验证服务状态：
```bash
curl http://localhost:8000/health
```

### 步骤 2: 在 Dify 中创建自定义工具

1. 登录 Dify 管理后台
2. 进入 **工具** → **自定义工具**
3. 点击 **创建工具**
4. 选择 **OpenAPI** 类型

### 步骤 3: 填写基本信息

```
工具名称: Web Scraping Agent
工具标识: web_scraping_agent
描述: 智能网页爬虫，支持自然语言输入和自动登录
图标: 🕷️
```

### 步骤 4: 导入 Schema

**方法 1: 直接粘贴 Schema**

复制 `dify_tool_schema_simple.json` 的内容，粘贴到 Schema 输入框中。

**方法 2: 修改服务器 URL**

在粘贴前，将 Schema 中的服务器 URL 修改为你的实际地址：

```json
{
  "servers": [
    {
      "url": "http://your-actual-server:8000"
    }
  ]
}
```

### 步骤 5: 完整的 Schema 配置

```json
{
  "openapi": "3.1.0",
  "info": {
    "title": "Web Scraping Agent",
    "description": "Intelligent web scraping with natural language input",
    "version": "v1.0.0"
  },
  "servers": [
    {
      "url": "http://your-server:8000"
    }
  ],
  "paths": {
    "/tools/web-scraping": {
      "post": {
        "description": "Extract content from web pages using natural language descriptions",
        "operationId": "WebScraping",
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "required": ["description"],
                "properties": {
                  "description": {
                    "type": "string",
                    "description": "Natural language description of what to scrape"
                  },
                  "url": {
                    "type": "string",
                    "description": "Target webpage URL (optional if in description)"
                  },
                  "username": {
                    "type": "string",
                    "description": "Login username (if authentication needed)"
                  },
                  "password": {
                    "type": "string",
                    "description": "Login password (if authentication needed)"
                  }
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "Scraping result",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "status": {
                      "type": "string"
                    },
                    "data": {
                      "type": "object"
                    },
                    "summary": {
                      "type": "string"
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  },
  "components": {
    "schemas": {}
  }
}
```

### 步骤 6: 测试工具

在 Dify 工具配置页面，点击 **测试** 按钮：

**测试参数:**
```json
{
  "description": "获取百度首页的标题",
  "url": "https://www.baidu.com"
}
```

**预期响应:**
```json
{
  "status": "success",
  "data": {
    "title": "百度一下，你就知道"
  },
  "summary": "成功提取网页标题"
}
```

### 步骤 7: 在应用中使用

#### 聊天应用配置
```yaml
tools:
  - name: web_scraping_agent
    enabled: true
```

#### Agent 应用配置
```yaml
agent:
  tools:
    - web_scraping_agent
  instructions: |
    你是一个智能助手，可以帮助用户获取网页信息。
    当用户需要爬取网页内容时，使用 web_scraping_agent 工具。
    支持自然语言描述爬取需求。
```

### 步骤 8: 用户使用示例

用户可以在 Dify 应用中这样提问：

```
用户: "帮我爬取这个商品页面的价格和评论: https://shop.example.com/product/123"

系统: 调用 web_scraping_agent 工具
参数: {
  "description": "爬取商品页面的价格和评论",
  "url": "https://shop.example.com/product/123"
}

返回: {
  "status": "success",
  "data": {
    "product_name": "iPhone 15 Pro",
    "price": "$999.00",
    "reviews": ["很好用", "物流快", "推荐购买"]
  },
  "summary": "成功提取商品信息"
}
```

## 🔒 安全配置 (可选)

如果你的服务器需要认证，可以在 Dify 工具配置中添加认证头：

### 在工具配置中添加认证
```json
{
  "headers": {
    "Authorization": "Bearer your-api-token"
  }
}
```

### 在服务器端设置 API Token
```bash
export API_TOKEN=your-secret-token
```

## 🛠️ 故障排除

### 常见问题

1. **连接失败**
   - 检查服务器 URL 是否正确
   - 确认服务器正在运行
   - 检查防火墙设置

2. **Schema 验证失败**
   - 确认 JSON 格式正确
   - 检查 OpenAPI 版本兼容性
   - 验证必需字段

3. **工具调用失败**
   - 查看服务器日志
   - 检查参数格式
   - 验证 API 端点

### 调试命令

```bash
# 检查服务状态
curl http://your-server:8000/health

# 测试工具端点
curl -X POST http://your-server:8000/tools/web-scraping \
  -H "Content-Type: application/json" \
  -d '{
    "description": "获取百度首页标题",
    "url": "https://www.baidu.com"
  }'

# 查看服务日志
docker-compose logs web-scraping-agent
```

## 🎉 完成

现在你的 Web Scraping Agent 已经成功集成到 Dify 中！用户可以通过自然语言描述来爬取任何网页的内容。
