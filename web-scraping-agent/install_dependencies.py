#!/usr/bin/env python3
"""
安装HTML压缩优化所需的依赖包
"""

import subprocess
import sys
import os

def install_package(package):
    """安装单个包"""
    try:
        print(f"正在安装 {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package} 安装失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始安装HTML压缩优化依赖包...")
    
    # 需要安装的包
    packages = [
        "htmlmin==0.1.12",
        "html2text==2020.1.16"
    ]
    
    success_count = 0
    total_count = len(packages)
    
    for package in packages:
        if install_package(package):
            success_count += 1
    
    print(f"\n📊 安装结果: {success_count}/{total_count} 个包安装成功")
    
    if success_count == total_count:
        print("🎉 所有依赖包安装完成！")
        print("\n新增功能:")
        print("- htmlmin: HTML基础压缩，移除空白和注释")
        print("- html2text: HTML转文本，极大减少内容大小")
        print("- BeautifulSoup智能提取: 精确提取商品相关内容")
        print("- 优化的system prompt: 减少不必要的说明文字")
        print("\n预期效果:")
        print("- HTML内容大小减少50-70%")
        print("- System prompt大小减少20-30%")
        print("- 总体token使用量减少40-60%")
    else:
        print("⚠️  部分依赖包安装失败，请手动安装")
        print("手动安装命令:")
        for package in packages:
            print(f"  pip install {package}")

if __name__ == "__main__":
    main()
