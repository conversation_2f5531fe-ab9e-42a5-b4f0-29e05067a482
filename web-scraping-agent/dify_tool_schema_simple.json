{"openapi": "3.1.0", "info": {"title": "Web Scraping Agent", "description": "Intelligent web scraping with natural language input", "version": "v1.0.0"}, "servers": [{"url": "http://your-server:8000"}], "paths": {"/tools/web-scraping": {"post": {"description": "Extract content from web pages using natural language descriptions", "operationId": "WebScraping", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["description"], "properties": {"description": {"type": "string", "description": "Natural language description of what to scrape"}, "url": {"type": "string", "description": "Target webpage URL (optional if in description)"}, "username": {"type": "string", "description": "Login username (if authentication needed)"}, "password": {"type": "string", "description": "Login password (if authentication needed)"}}}}}}, "responses": {"200": {"description": "Scraping result", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string"}, "data": {"type": "object"}, "summary": {"type": "string"}}}}}}}}}}, "components": {"schemas": {}}}