#!/usr/bin/env python3
"""
快速启动 Web Scraping Agent 服务器

用于本地开发和测试
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path


def check_requirements():
    """检查必要的依赖"""
    print("🔍 检查依赖...")
    
    required_packages = [
        'fastapi',
        'uvicorn',
        'openai',
        'playwright'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("💡 请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 依赖检查通过")
    return True


def check_playwright():
    """检查 Playwright 浏览器"""
    print("🌐 检查 Playwright 浏览器...")
    
    try:
        result = subprocess.run(
            ['playwright', 'install', '--dry-run', 'chromium'],
            capture_output=True,
            text=True
        )
        
        if 'chromium' not in result.stdout.lower():
            print("⚠️  Chromium 浏览器未安装")
            print("💡 正在安装 Chromium...")
            subprocess.run(['playwright', 'install', 'chromium'])
        
        print("✅ Playwright 浏览器检查通过")
        return True
        
    except FileNotFoundError:
        print("❌ Playwright 未安装")
        print("💡 请运行: pip install playwright && playwright install chromium")
        return False


def check_environment():
    """检查环境变量"""
    print("🔑 检查环境变量...")
    
    openai_key = os.getenv('OPENAI_API_KEY')
    
    if not openai_key:
        print("⚠️  未设置 OPENAI_API_KEY 环境变量")
        
        # 尝试从 .env 文件读取
        env_file = Path('.env')
        if env_file.exists():
            print("📄 发现 .env 文件，尝试加载...")
            try:
                from dotenv import load_dotenv
                load_dotenv()
                openai_key = os.getenv('OPENAI_API_KEY')
            except ImportError:
                print("💡 请安装 python-dotenv: pip install python-dotenv")
        
        if not openai_key:
            print("❌ 请设置 OPENAI_API_KEY")
            print("方法1: export OPENAI_API_KEY='your-api-key'")
            print("方法2: 创建 .env 文件并添加 OPENAI_API_KEY=your-api-key")
            return False
    
    print("✅ 环境变量检查通过")
    return True


def start_server():
    """启动服务器"""
    print("🚀 启动 Web Scraping Agent 服务器...")
    
    # 设置默认配置
    host = os.getenv('HOST', '127.0.0.1')
    port = int(os.getenv('PORT', '8000'))
    
    print(f"📡 服务器地址: http://{host}:{port}")
    print(f"📚 API 文档: http://{host}:{port}/docs")
    print(f"🔍 健康检查: http://{host}:{port}/health")
    print()
    
    try:
        # 启动 uvicorn 服务器
        subprocess.run([
            'uvicorn',
            'dify_api_server:app',
            '--host', host,
            '--port', str(port),
            '--reload',
            '--log-level', 'info'
        ])
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")


def test_server():
    """测试服务器"""
    print("🧪 测试服务器...")
    
    host = os.getenv('HOST', '127.0.0.1')
    port = int(os.getenv('PORT', '8000'))
    base_url = f"http://{host}:{port}"
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    for i in range(30):
        try:
            response = requests.get(f"{base_url}/health", timeout=5)
            if response.status_code == 200:
                print("✅ 服务器启动成功")
                break
        except requests.exceptions.RequestException:
            time.sleep(1)
    else:
        print("❌ 服务器启动超时")
        return False
    
    # 测试基本功能
    try:
        # 测试健康检查
        response = requests.get(f"{base_url}/health")
        print(f"🏥 健康检查: {response.status_code}")
        
        # 测试能力查询
        response = requests.get(f"{base_url}/capabilities")
        print(f"🎯 能力查询: {response.status_code}")
        
        # 测试简单爬取
        test_data = {
            "description": "获取百度首页的标题",
            "url": "https://www.baidu.com"
        }
        
        response = requests.post(f"{base_url}/scrape/simple", json=test_data)
        print(f"🕷️  爬取测试: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 爬取功能正常")
            else:
                print(f"⚠️  爬取返回错误: {result.get('error')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def show_usage():
    """显示使用说明"""
    print("📖 Web Scraping Agent 使用说明")
    print("=" * 50)
    
    print("\n🔧 API 端点:")
    print("  GET  /health          - 健康检查")
    print("  GET  /capabilities    - 获取能力描述")
    print("  POST /scrape          - 完整爬取接口")
    print("  POST /scrape/simple   - 简化爬取接口")
    print("  POST /tools/web-scraping - Dify 工具接口")
    
    print("\n📝 使用示例:")
    print("  curl -X POST http://localhost:8000/scrape/simple \\")
    print("    -H 'Content-Type: application/json' \\")
    print("    -d '{\"description\": \"获取百度首页标题\", \"url\": \"https://www.baidu.com\"}'")
    
    print("\n🔗 Dify 集成:")
    print("  1. 在 Dify 中添加自定义工具")
    print("  2. 配置 API 端点: http://your-server:8000/tools/web-scraping")
    print("  3. 参考 DIFY_INTEGRATION_GUIDE.md 获取详细说明")
    
    print("\n📚 更多信息:")
    print("  - API 文档: http://localhost:8000/docs")
    print("  - 集成指南: DIFY_INTEGRATION_GUIDE.md")
    print("  - 使用文档: README_intelligent_scraper.md")


def main():
    """主函数"""
    print("🤖 Web Scraping Agent 快速启动")
    print("=" * 50)
    
    # 检查依赖
    if not check_requirements():
        sys.exit(1)
    
    # 检查 Playwright
    if not check_playwright():
        sys.exit(1)
    
    # 检查环境变量
    if not check_environment():
        sys.exit(1)
    
    print("\n✅ 所有检查通过，准备启动服务器...")
    print("💡 按 Ctrl+C 停止服务器")
    print("=" * 50)
    
    # 显示使用说明
    show_usage()
    
    print("\n" + "=" * 50)
    input("按 Enter 键启动服务器...")
    
    # 启动服务器
    start_server()


if __name__ == "__main__":
    main()
