"""
Unit tests for HTML loaders
"""

import unittest
import tempfile
import os
from pathlib import Path
from unittest.mock import patch, MagicMock

import sys
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.html_loaders import (
    HTMLLoaderFactory,
    SmartHTMLLoader,
    BatchHTMLLoader,
    StructuredHTMLLoader
)
from core.config import LoaderConfig


class TestHTMLLoaderFactory(unittest.TestCase):
    """Test HTML loader factory"""
    
    def test_create_smart_loader(self):
        """Test creating smart loader"""
        loader = HTMLLoaderFactory.create_loader("smart")
        self.assertIsInstance(loader, SmartHTMLLoader)
    
    def test_create_batch_loader(self):
        """Test creating batch loader"""
        loader = HTMLLoaderFactory.create_loader("batch")
        self.assertIsInstance(loader, BatchHTMLLoader)
    
    def test_create_structured_loader(self):
        """Test creating structured loader"""
        loader = HTMLLoaderFactory.create_loader("structured")
        self.assertIsInstance(loader, StructuredHTMLLoader)
    
    def test_invalid_loader_type(self):
        """Test invalid loader type raises error"""
        with self.assertRaises(ValueError):
            HTMLLoaderFactory.create_loader("invalid")
    
    def test_get_best_loader_for_single_url(self):
        """Test best loader selection for single URL"""
        loader = HTMLLoaderFactory.get_best_loader("https://example.com")
        self.assertIsInstance(loader, SmartHTMLLoader)
    
    def test_get_best_loader_for_multiple_urls(self):
        """Test best loader selection for multiple URLs"""
        urls = ["https://example.com", "https://test.com"]
        loader = HTMLLoaderFactory.get_best_loader(urls)
        # Should return DynamicHTMLLoader for small lists, BatchHTMLLoader for large lists
        self.assertIsNotNone(loader)


class TestSmartHTMLLoader(unittest.TestCase):
    """Test smart HTML loader"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.config = LoaderConfig(
            requests_per_second=10.0,  # Fast for testing
            timeout=5,
            retries=1
        )
        self.loader = SmartHTMLLoader(self.config)
        
        # Create test HTML content
        self.test_html = """
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <title>Test Page</title>
            <meta name="description" content="Test description">
        </head>
        <body>
            <header>
                <nav>Navigation</nav>
            </header>
            <main>
                <article>
                    <h1>Main Title</h1>
                    <p>This is the main content of the test page.</p>
                    <p>It contains multiple paragraphs for testing.</p>
                </article>
            </main>
            <footer>Footer content</footer>
        </body>
        </html>
        """
    
    def test_load_from_file(self):
        """Test loading HTML from file"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
            f.write(self.test_html)
            temp_file = f.name
        
        try:
            documents = self.loader.load(temp_file)
            
            self.assertEqual(len(documents), 1)
            doc = documents[0]
            
            self.assertIn("Main Title", doc.page_content)
            self.assertIn("main content", doc.page_content)
            self.assertEqual(doc.metadata['type'], 'file')
            self.assertEqual(doc.metadata['source'], temp_file)
            
        finally:
            os.unlink(temp_file)
    
    def test_load_empty_file(self):
        """Test loading empty HTML file"""
        empty_html = "<html><body></body></html>"
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
            f.write(empty_html)
            temp_file = f.name
        
        try:
            documents = self.loader.load(temp_file)
            
            self.assertEqual(len(documents), 1)
            # Should handle empty content gracefully
            
        finally:
            os.unlink(temp_file)
    
    def test_load_nonexistent_file(self):
        """Test loading non-existent file raises error"""
        with self.assertRaises(Exception):
            self.loader.load("nonexistent_file.html")
    
    @patch('requests.Session.get')
    def test_load_from_url(self, mock_get):
        """Test loading HTML from URL"""
        # Mock HTTP response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.content = self.test_html.encode('utf-8')
        mock_response.encoding = 'utf-8'
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        documents = self.loader.load("https://example.com")
        
        self.assertEqual(len(documents), 1)
        doc = documents[0]
        
        self.assertIn("Main Title", doc.page_content)
        self.assertEqual(doc.metadata['type'], 'url')
        self.assertEqual(doc.metadata['source'], "https://example.com")
        self.assertEqual(doc.metadata['status_code'], 200)
    
    @patch('requests.Session.get')
    def test_load_url_with_error(self, mock_get):
        """Test loading URL with HTTP error"""
        mock_get.side_effect = Exception("Connection error")
        
        with self.assertRaises(Exception):
            self.loader.load("https://example.com")
    
    def test_extract_content(self):
        """Test content extraction"""
        content = self.loader._extract_content(self.test_html)
        
        # Should extract main content
        self.assertIn("Main Title", content)
        self.assertIn("main content", content)
        
        # Should not include navigation or footer
        self.assertNotIn("Navigation", content)
        self.assertNotIn("Footer content", content)
    
    def test_invalid_source(self):
        """Test invalid source raises error"""
        with self.assertRaises(ValueError):
            self.loader.load("not-a-url-or-file")


class TestBatchHTMLLoader(unittest.TestCase):
    """Test batch HTML loader"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.config = LoaderConfig(
            requests_per_second=10.0,
            timeout=5,
            retries=1
        )
        self.loader = BatchHTMLLoader(self.config)
    
    @patch('aiohttp.ClientSession.get')
    async def test_load_async(self, mock_get):
        """Test async loading"""
        # Mock aiohttp response
        mock_response = MagicMock()
        mock_response.status = 200
        mock_response.text.return_value = "<html><body><p>Test content</p></body></html>"
        
        mock_get.return_value.__aenter__.return_value = mock_response
        
        urls = ["https://example.com", "https://test.com"]
        documents = await self.loader.load_async(urls)
        
        # Should return documents for successful requests
        self.assertIsInstance(documents, list)
    
    def test_extract_content(self):
        """Test content extraction for batch loader"""
        html = "<html><body><main><p>Main content here</p></main></body></html>"
        content = self.loader._extract_content(html)
        
        self.assertIn("Main content here", content)


class TestStructuredHTMLLoader(unittest.TestCase):
    """Test structured HTML loader"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.loader = StructuredHTMLLoader()
    
    def test_fallback_to_smart_loader(self):
        """Test fallback when UnstructuredHTMLLoader not available"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
            f.write("<html><body><p>Test content</p></body></html>")
            temp_file = f.name
        
        try:
            # Should fallback to SmartHTMLLoader if Unstructured not available
            documents = self.loader.load(temp_file)
            self.assertIsInstance(documents, list)
            
        finally:
            os.unlink(temp_file)


if __name__ == '__main__':
    unittest.main()
