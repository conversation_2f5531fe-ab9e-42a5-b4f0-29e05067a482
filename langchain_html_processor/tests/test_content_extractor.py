"""
Unit tests for content extractors
"""

import unittest
from pathlib import Path

import sys
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.content_extractor import (
    ContentExtractor,
    SmartContentExtractor,
    LayoutAnalyzer,
    ExtractedContent
)
from core.config import ExtractorConfig


class TestContentExtractor(unittest.TestCase):
    """Test basic content extractor"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.config = ExtractorConfig(
            extract_links=True,
            extract_images=True,
            detect_language=False  # Disable for testing
        )
        self.extractor = ContentExtractor(self.config)
        
        self.test_html = """
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <title>Test Article</title>
            <meta name="description" content="This is a test article for extraction">
        </head>
        <body>
            <header>
                <nav>
                    <a href="/">Home</a>
                    <a href="/about">About</a>
                </nav>
            </header>
            
            <main>
                <article>
                    <h1>Main Article Title</h1>
                    <p>This is the first paragraph of the main article content. 
                    It contains <a href="https://example.com">external links</a> and important information.</p>
                    
                    <p>This is the second paragraph with more detailed content about the topic.</p>
                    
                    <img src="https://example.com/image.jpg" alt="Test image" title="Test image title">
                    
                    <h2>Subsection</h2>
                    <p>More content in a subsection with additional details.</p>
                </article>
            </main>
            
            <aside>
                <div class="sidebar">
                    <p>Sidebar content that should be filtered out.</p>
                </div>
            </aside>
            
            <footer>
                <p>Footer content</p>
            </footer>
            
            <script>
                console.log("This script should be removed");
            </script>
        </body>
        </html>
        """
    
    def test_extract_basic_content(self):
        """Test basic content extraction"""
        extracted = self.extractor.extract(self.test_html)
        
        self.assertIsInstance(extracted, ExtractedContent)
        self.assertIsNotNone(extracted.text)
        self.assertIn("Main Article Title", extracted.text)
        self.assertIn("first paragraph", extracted.text)
    
    def test_extract_title(self):
        """Test title extraction"""
        extracted = self.extractor.extract(self.test_html)
        self.assertEqual(extracted.title, "Test Article")
    
    def test_extract_description(self):
        """Test description extraction"""
        extracted = self.extractor.extract(self.test_html)
        self.assertEqual(extracted.description, "This is a test article for extraction")
    
    def test_extract_links(self):
        """Test link extraction"""
        extracted = self.extractor.extract(self.test_html, "https://test.com")
        
        self.assertGreater(len(extracted.links), 0)
        
        # Check for external link
        external_links = [link for link in extracted.links if link['url'] == 'https://example.com']
        self.assertEqual(len(external_links), 1)
        self.assertEqual(external_links[0]['text'], 'external links')
        
        # Check for navigation links (converted to absolute URLs)
        nav_links = [link for link in extracted.links if link['url'] == 'https://test.com/']
        self.assertEqual(len(nav_links), 1)
        self.assertEqual(nav_links[0]['text'], 'Home')
    
    def test_extract_images(self):
        """Test image extraction"""
        extracted = self.extractor.extract(self.test_html)
        
        self.assertEqual(len(extracted.images), 1)
        image = extracted.images[0]
        
        self.assertEqual(image['url'], 'https://example.com/image.jpg')
        self.assertEqual(image['alt'], 'Test image')
        self.assertEqual(image['title'], 'Test image title')
    
    def test_clean_html(self):
        """Test HTML cleaning"""
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(self.test_html, 'html.parser')
        
        # Before cleaning - should have script tags
        self.assertIsNotNone(soup.find('script'))
        
        self.extractor._clean_html(soup)
        
        # After cleaning - script tags should be removed
        self.assertIsNone(soup.find('script'))
    
    def test_extract_main_text(self):
        """Test main text extraction"""
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(self.test_html, 'html.parser')
        
        main_text = self.extractor._extract_main_text(soup)
        
        # Should contain main content
        self.assertIn("Main Article Title", main_text)
        self.assertIn("first paragraph", main_text)
        
        # Should not contain navigation or footer
        self.assertNotIn("Footer content", main_text)
    
    def test_metadata_generation(self):
        """Test metadata generation"""
        extracted = self.extractor.extract(self.test_html)
        
        self.assertIn('word_count', extracted.metadata)
        self.assertIn('char_count', extracted.metadata)
        self.assertIn('link_count', extracted.metadata)
        self.assertIn('image_count', extracted.metadata)
        
        self.assertGreater(extracted.metadata['word_count'], 0)
        self.assertGreater(extracted.metadata['char_count'], 0)
        self.assertEqual(extracted.metadata['link_count'], len(extracted.links))
        self.assertEqual(extracted.metadata['image_count'], len(extracted.images))


class TestSmartContentExtractor(unittest.TestCase):
    """Test smart content extractor"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.extractor = SmartContentExtractor()
        
        # HTML with low content density
        self.low_density_html = """
        <html>
        <body>
            <div class="container">
                <div class="wrapper">
                    <div class="content-area">
                        <div class="main-content">
                            <p>This is the actual content that should be extracted.</p>
                            <p>It has good text density compared to HTML markup.</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="noise">
                <div><div><div><span></span></div></div></div>
                <p>Noise</p>
            </div>
        </body>
        </html>
        """
    
    def test_density_based_extraction(self):
        """Test density-based content extraction"""
        content = self.extractor._extract_by_density(
            self.extractor._create_soup(self.low_density_html)
        )
        
        self.assertIn("actual content", content)
        self.assertIn("good text density", content)
    
    def _create_soup(self, html):
        """Helper to create BeautifulSoup object"""
        from bs4 import BeautifulSoup
        return BeautifulSoup(html, 'html.parser')


class TestLayoutAnalyzer(unittest.TestCase):
    """Test layout analyzer"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.analyzer = LayoutAnalyzer()
        
        self.test_html = """
        <html>
        <body>
            <header class="site-header">Header</header>
            <nav class="main-nav">Navigation</nav>
            <main class="content">
                <article class="post">Main content</article>
            </main>
            <aside class="sidebar">Sidebar</aside>
            <footer class="site-footer">Footer</footer>
        </body>
        </html>
        """
    
    def test_analyze_layout(self):
        """Test layout analysis"""
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(self.test_html, 'html.parser')
        
        analysis = self.analyzer.analyze_layout(soup)
        
        self.assertIn('has_header', analysis)
        self.assertIn('has_footer', analysis)
        self.assertIn('has_sidebar', analysis)
        self.assertIn('has_navigation', analysis)
        self.assertIn('main_content_area', analysis)
        self.assertIn('content_blocks', analysis)
        
        self.assertTrue(analysis['has_header'])
        self.assertTrue(analysis['has_footer'])
        self.assertTrue(analysis['has_sidebar'])
        self.assertTrue(analysis['has_navigation'])
    
    def test_find_main_content_area(self):
        """Test finding main content area"""
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(self.test_html, 'html.parser')
        
        main_area = self.analyzer._find_main_content_area(soup)
        self.assertIsNotNone(main_area)
    
    def test_identify_content_blocks(self):
        """Test content block identification"""
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(self.test_html, 'html.parser')
        
        blocks = self.analyzer._identify_content_blocks(soup)
        
        self.assertIsInstance(blocks, list)
        self.assertGreater(len(blocks), 0)
        
        # Check block structure
        for block in blocks:
            self.assertIn('tag', block)
            self.assertIn('classes', block)
            self.assertIn('type', block)
            self.assertIn('text_length', block)
    
    def test_classify_block(self):
        """Test block classification"""
        from bs4 import BeautifulSoup
        
        # Test different block types
        test_cases = [
            ('<nav class="navigation">Nav</nav>', 'navigation'),
            ('<div class="sidebar">Side</div>', 'sidebar'),
            ('<footer>Footer</footer>', 'footer'),
            ('<header>Header</header>', 'header'),
            ('<main class="content">Content</main>', 'main_content'),
            ('<div class="advertisement">Ad</div>', 'advertisement'),
            ('<div class="unknown">Unknown</div>', 'unknown')
        ]
        
        for html, expected_type in test_cases:
            soup = BeautifulSoup(html, 'html.parser')
            element = soup.find()
            block_type = self.analyzer._classify_block(element)
            self.assertEqual(block_type, expected_type, f"Failed for {html}")


class TestExtractorConfig(unittest.TestCase):
    """Test extractor configuration"""
    
    def test_default_config(self):
        """Test default configuration"""
        config = ExtractorConfig()
        
        self.assertIsInstance(config.content_selectors, list)
        self.assertIsInstance(config.remove_selectors, list)
        self.assertIsInstance(config.supported_languages, list)
        
        self.assertGreater(len(config.content_selectors), 0)
        self.assertGreater(len(config.remove_selectors), 0)
    
    def test_custom_config(self):
        """Test custom configuration"""
        config = ExtractorConfig(
            content_selectors=['.custom-content'],
            remove_selectors=['.custom-remove'],
            min_text_length=200,
            extract_links=False,
            detect_language=False
        )
        
        self.assertEqual(config.content_selectors, ['.custom-content'])
        self.assertEqual(config.remove_selectors, ['.custom-remove'])
        self.assertEqual(config.min_text_length, 200)
        self.assertFalse(config.extract_links)
        self.assertFalse(config.detect_language)


if __name__ == '__main__':
    unittest.main()
