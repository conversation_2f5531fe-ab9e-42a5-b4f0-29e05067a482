"""
LangChain HTML Processor

A comprehensive HTML processing system based on <PERSON><PERSON>hai<PERSON> with support for:
- Multiple HTML loading strategies (static, dynamic, batch, structured)
- Intelligent content extraction with layout analysis
- Advanced processing pipelines with chunking and embeddings
- Production-ready features (caching, rate limiting, error handling)

Quick Start:
    from langchain_html_processor import HTMLLoaderFactory, HTMLProcessingPipeline
    
    # Load HTML content
    loader = HTMLLoaderFactory.create_loader("smart")
    documents = loader.load("https://example.com")
    
    # Process through complete pipeline
    pipeline = HTMLProcessingPipeline()
    result = pipeline.process("https://example.com")

For more examples, see the examples/ directory and README.md
"""

from .core import (
    # Main classes
    HTMLLoaderFactory,
    HTMLProcessingPipeline,
    
    # Loaders
    SmartHTMLLoader,
    BatchHTMLLoader,
    DynamicHTMLLoader,
    StructuredHTMLLoader,
    AuthenticatedHTMLLoader,
    
    # Extractors
    ContentExtractor,
    SmartContentExtractor,
    LayoutAnalyzer,
    
    # Configuration
    Config,
    LoaderConfig,
    ExtractorConfig,
    PipelineConfig,
    
    # Results
    ProcessingResult,
    
    # Utilities
    EncodingDetector,
    ProxyRotator,
    CacheManager,
    RateLimiter,
    ErrorHandler
)

# Convenience functions
from .core.html_loaders import (
    load_html_file,
    load_html_url,
    load_html_urls,
    load_dynamic_html
)

from .core.processing_pipeline import (
    process_html_file,
    process_html_url,
    process_html_urls
)

__version__ = "1.0.0"
__author__ = "LangChain HTML Processor Team"
__email__ = "<EMAIL>"
__description__ = "Comprehensive HTML processing system for LangChain"

__all__ = [
    # Main classes
    "HTMLLoaderFactory",
    "HTMLProcessingPipeline",
    
    # Loaders
    "SmartHTMLLoader",
    "BatchHTMLLoader",
    "DynamicHTMLLoader",
    "StructuredHTMLLoader",
    "AuthenticatedHTMLLoader",
    
    # Extractors
    "ContentExtractor",
    "SmartContentExtractor",
    "LayoutAnalyzer",
    
    # Configuration
    "Config",
    "LoaderConfig",
    "ExtractorConfig", 
    "PipelineConfig",
    
    # Results
    "ProcessingResult",
    
    # Utilities
    "EncodingDetector",
    "ProxyRotator",
    "CacheManager", 
    "RateLimiter",
    "ErrorHandler",
    
    # Convenience functions
    "load_html_file",
    "load_html_url",
    "load_html_urls",
    "load_dynamic_html",
    "process_html_file",
    "process_html_url", 
    "process_html_urls"
]


def get_version():
    """Get package version"""
    return __version__


def get_info():
    """Get package information"""
    return {
        "name": "langchain-html-processor",
        "version": __version__,
        "author": __author__,
        "email": __email__,
        "description": __description__,
        "features": [
            "Multiple HTML loading strategies",
            "Intelligent content extraction", 
            "Advanced processing pipelines",
            "Production-ready features"
        ]
    }


# Package-level configuration
import logging

# Set up default logging
logging.getLogger(__name__).addHandler(logging.NullHandler())

# Optional: Set up basic configuration if no handlers exist
if not logging.getLogger().handlers:
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
