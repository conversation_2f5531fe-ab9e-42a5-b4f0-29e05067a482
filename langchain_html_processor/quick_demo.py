#!/usr/bin/env python3
"""
Quick demonstration of LangChain HTML Processor capabilities
"""

import sys
import tempfile
from pathlib import Path

# Add the package to Python path
sys.path.insert(0, str(Path(__file__).parent))

from core import (
    HTMLLoaderFactory,
    HTMLProcessingPipeline,
    SmartContentExtractor,
    Config,
    LoaderConfig,
    ExtractorConfig,
    PipelineConfig
)


def main():
    print("🚀 LangChain HTML Processor - Quick Demo")
    print("=" * 50)
    
    # Create sample HTML content
    sample_html = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <title>AI and Machine Learning in 2024</title>
        <meta name="description" content="Exploring the latest trends in AI and ML">
    </head>
    <body>
        <header>
            <nav>
                <a href="/">Home</a>
                <a href="/about">About</a>
            </nav>
        </header>
        
        <main>
            <article>
                <h1>The Future of Artificial Intelligence</h1>
                
                <p>Artificial Intelligence continues to evolve at an unprecedented pace. 
                In 2024, we're seeing remarkable advances in large language models, 
                computer vision, and autonomous systems.</p>
                
                <h2>Key Developments</h2>
                <p>Some of the most significant developments include:</p>
                <ul>
                    <li>Multimodal AI systems that can process text, images, and audio</li>
                    <li>More efficient training methods reducing computational costs</li>
                    <li>Better alignment techniques for safer AI systems</li>
                    <li>Integration of AI into everyday applications</li>
                </ul>
                
                <h2>Challenges and Opportunities</h2>
                <p>While the progress is exciting, we also face important challenges 
                around ethics, bias, and ensuring AI benefits everyone. The key is 
                to develop these technologies responsibly.</p>
                
                <blockquote>
                    "The best way to predict the future is to invent it." - Alan Kay
                </blockquote>
                
                <p>As we move forward, collaboration between researchers, developers, 
                and policymakers will be crucial for realizing AI's full potential 
                while mitigating risks.</p>
            </article>
        </main>
        
        <footer>
            <p>© 2024 AI Research Blog</p>
        </footer>
    </body>
    </html>
    """
    
    # Demo 1: Basic HTML Loading
    print("\n📄 Demo 1: Basic HTML Loading")
    print("-" * 30)
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
        f.write(sample_html)
        temp_file = f.name
    
    try:
        loader = HTMLLoaderFactory.create_loader("smart")
        documents = loader.load(temp_file)
        
        print(f"✅ Loaded {len(documents)} document(s)")
        doc = documents[0]
        print(f"   Source: {doc.metadata.get('source')}")
        print(f"   Content length: {len(doc.page_content)} characters")
        print(f"   Preview: {doc.page_content[:100]}...")
        
    finally:
        Path(temp_file).unlink()
    
    # Demo 2: Advanced Content Extraction
    print("\n🔍 Demo 2: Advanced Content Extraction")
    print("-" * 40)
    
    config = ExtractorConfig(
        extract_links=True,
        extract_images=True,
        detect_language=False  # Disabled for demo
    )
    
    extractor = SmartContentExtractor(config)
    extracted = extractor.extract(sample_html)
    
    print(f"✅ Content extracted successfully")
    print(f"   Title: {extracted.title}")
    print(f"   Description: {extracted.description}")
    print(f"   Word count: {extracted.metadata.get('word_count', 0)}")
    print(f"   Links found: {len(extracted.links)}")
    print(f"   Main content preview: {extracted.text[:150]}...")
    
    if extracted.links:
        print(f"   Sample links:")
        for link in extracted.links[:2]:
            print(f"     - {link['text']}: {link['url']}")
    
    # Demo 3: Complete Processing Pipeline
    print("\n⚙️  Demo 3: Complete Processing Pipeline")
    print("-" * 42)
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
        f.write(sample_html)
        temp_file = f.name
    
    try:
        # Configure pipeline
        config = Config(
            pipeline=PipelineConfig(
                chunk_size=300,
                chunk_overlap=50,
                enable_cache=True
            )
        )
        
        pipeline = HTMLProcessingPipeline(config)
        result = pipeline.process(temp_file, create_vector_store=False)
        
        print(f"✅ Pipeline processing completed")
        print(f"   Documents processed: {len(result.documents)}")
        print(f"   Chunks created: {len(result.chunks)}")
        print(f"   Total words: {result.metadata.get('total_words', 0)}")
        print(f"   Average chunk size: {result.metadata.get('average_chunk_size', 0):.0f} chars")
        
        # Show first chunk
        if result.chunks:
            chunk = result.chunks[0]
            print(f"   First chunk preview: {chunk.page_content[:100]}...")
            print(f"   Chunk metadata: {chunk.metadata.get('chunk_id')}")
        
    finally:
        Path(temp_file).unlink()
    
    # Demo 4: Configuration Options
    print("\n⚙️  Demo 4: Configuration Options")
    print("-" * 35)
    
    # Show different configurations
    configs = {
        "Conservative": LoaderConfig(
            requests_per_second=0.5,
            timeout=60,
            retries=5
        ),
        "Balanced": LoaderConfig(
            requests_per_second=2.0,
            timeout=30,
            retries=3
        ),
        "Aggressive": LoaderConfig(
            requests_per_second=5.0,
            timeout=10,
            retries=1
        )
    }
    
    print("✅ Available configuration profiles:")
    for name, config in configs.items():
        print(f"   {name}:")
        print(f"     - Rate: {config.requests_per_second} req/sec")
        print(f"     - Timeout: {config.timeout}s")
        print(f"     - Retries: {config.retries}")
    
    # Demo 5: Error Handling
    print("\n🛡️  Demo 5: Error Handling")
    print("-" * 28)
    
    loader = HTMLLoaderFactory.create_loader("smart")
    
    # Test graceful error handling
    test_cases = [
        ("Invalid file", "nonexistent.html"),
        ("Empty content", "<html></html>"),
    ]
    
    for test_name, test_input in test_cases:
        try:
            if test_name == "Empty content":
                with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
                    f.write(test_input)
                    temp_file = f.name
                
                try:
                    docs = loader.load(temp_file)
                    print(f"✅ {test_name}: Handled gracefully ({len(docs)} docs)")
                finally:
                    Path(temp_file).unlink()
            else:
                docs = loader.load(test_input)
                print(f"✅ {test_name}: Unexpected success")
        except Exception as e:
            print(f"✅ {test_name}: Error handled gracefully ({type(e).__name__})")
    
    # Summary
    print("\n🎉 Demo Summary")
    print("-" * 15)
    print("✅ Basic HTML loading and processing")
    print("✅ Advanced content extraction with metadata")
    print("✅ Complete processing pipeline with chunking")
    print("✅ Flexible configuration options")
    print("✅ Robust error handling")
    print("\n📚 For more examples, see:")
    print("   - examples/basic_usage.py")
    print("   - examples/advanced_usage.py")
    print("   - demo.py (interactive demo)")
    print("\n🚀 Ready to process HTML content with LangChain!")


if __name__ == "__main__":
    main()
