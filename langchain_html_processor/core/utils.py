"""
Utility functions for Lang<PERSON>hain HTML Processor
"""

import time
import random
import sqlite3
import hashlib
import logging

try:
    import chardet
except ImportError:
    chardet = None
from typing import List, Optional, Dict, Any, Union
from pathlib import Path
from datetime import datetime, timedelta
from urllib.parse import urlparse
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry


class EncodingDetector:
    """Detect and handle text encoding"""
    
    @staticmethod
    def detect_encoding(content: bytes) -> str:
        """Detect encoding of byte content"""
        if chardet is None:
            return 'utf-8'

        try:
            result = chardet.detect(content)
            encoding = result.get('encoding', 'utf-8')
            confidence = result.get('confidence', 0)

            # Use utf-8 as fallback for low confidence
            if confidence < 0.7:
                encoding = 'utf-8'

            return encoding
        except Exception:
            return 'utf-8'
    
    @staticmethod
    def safe_decode(content: bytes, encoding: Optional[str] = None) -> str:
        """Safely decode bytes to string"""
        if encoding is None:
            encoding = EncodingDetector.detect_encoding(content)
        
        try:
            return content.decode(encoding)
        except UnicodeDecodeError:
            # Try common encodings
            for fallback in ['utf-8', 'gbk', 'gb2312', 'latin1']:
                try:
                    return content.decode(fallback)
                except UnicodeDecodeError:
                    continue
            
            # Last resort: decode with errors='ignore'
            return content.decode('utf-8', errors='ignore')


class ProxyRotator:
    """Rotate proxies for web scraping"""
    
    def __init__(self, proxies: List[str]):
        self.proxies = proxies
        self.current_index = 0
        self.failed_proxies = set()
    
    def get_proxy(self) -> Optional[Dict[str, str]]:
        """Get next available proxy"""
        if not self.proxies:
            return None
        
        available_proxies = [p for p in self.proxies if p not in self.failed_proxies]
        if not available_proxies:
            # Reset failed proxies if all failed
            self.failed_proxies.clear()
            available_proxies = self.proxies
        
        proxy = available_proxies[self.current_index % len(available_proxies)]
        self.current_index += 1
        
        return {
            'http': proxy,
            'https': proxy
        }
    
    def mark_failed(self, proxy: str):
        """Mark proxy as failed"""
        self.failed_proxies.add(proxy)


class CacheManager:
    """Simple SQLite-based cache manager"""
    
    def __init__(self, cache_path: str = "./.cache/html_cache.db", ttl: int = 3600):
        self.cache_path = Path(cache_path)
        self.cache_path.parent.mkdir(parents=True, exist_ok=True)
        self.ttl = ttl
        self._init_db()
    
    def _init_db(self):
        """Initialize cache database"""
        with sqlite3.connect(self.cache_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS cache (
                    key TEXT PRIMARY KEY,
                    value TEXT,
                    timestamp REAL,
                    expires_at REAL
                )
            """)
            conn.execute("CREATE INDEX IF NOT EXISTS idx_expires_at ON cache(expires_at)")
    
    def _generate_key(self, url: str, **kwargs) -> str:
        """Generate cache key from URL and parameters"""
        key_data = f"{url}:{str(sorted(kwargs.items()))}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def get(self, url: str, **kwargs) -> Optional[str]:
        """Get cached content"""
        key = self._generate_key(url, **kwargs)
        
        with sqlite3.connect(self.cache_path) as conn:
            cursor = conn.execute(
                "SELECT value FROM cache WHERE key = ? AND expires_at > ?",
                (key, time.time())
            )
            result = cursor.fetchone()
            return result[0] if result else None
    
    def set(self, url: str, content: str, **kwargs):
        """Cache content"""
        key = self._generate_key(url, **kwargs)
        timestamp = time.time()
        expires_at = timestamp + self.ttl
        
        with sqlite3.connect(self.cache_path) as conn:
            conn.execute(
                "INSERT OR REPLACE INTO cache (key, value, timestamp, expires_at) VALUES (?, ?, ?, ?)",
                (key, content, timestamp, expires_at)
            )
    
    def clear_expired(self):
        """Remove expired cache entries"""
        with sqlite3.connect(self.cache_path) as conn:
            conn.execute("DELETE FROM cache WHERE expires_at <= ?", (time.time(),))


class RateLimiter:
    """Rate limiting for web requests"""
    
    def __init__(self, requests_per_second: float = 2.0):
        self.requests_per_second = requests_per_second
        self.min_interval = 1.0 / requests_per_second
        self.last_request_time = 0
    
    def wait(self):
        """Wait if necessary to respect rate limit"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_interval:
            sleep_time = self.min_interval - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()


class ErrorHandler:
    """Handle and retry failed requests"""
    
    def __init__(self, max_retries: int = 3, backoff_factor: float = 1.0):
        self.max_retries = max_retries
        self.backoff_factor = backoff_factor
        self.logger = logging.getLogger(__name__)
    
    def create_session(self) -> requests.Session:
        """Create requests session with retry strategy"""
        session = requests.Session()
        
        retry_strategy = Retry(
            total=self.max_retries,
            backoff_factor=self.backoff_factor,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
    
    def retry_with_backoff(self, func, *args, **kwargs):
        """Retry function with exponential backoff"""
        for attempt in range(self.max_retries + 1):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if attempt == self.max_retries:
                    self.logger.error(f"Failed after {self.max_retries} attempts: {e}")
                    raise
                
                wait_time = self.backoff_factor * (2 ** attempt) + random.uniform(0, 1)
                self.logger.warning(f"Attempt {attempt + 1} failed: {e}. Retrying in {wait_time:.2f}s")
                time.sleep(wait_time)


class URLValidator:
    """Validate and normalize URLs"""
    
    @staticmethod
    def is_valid_url(url: str) -> bool:
        """Check if URL is valid"""
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False
    
    @staticmethod
    def normalize_url(url: str) -> str:
        """Normalize URL format"""
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
        return url.rstrip('/')


class ContentValidator:
    """Validate extracted content"""
    
    @staticmethod
    def is_valid_content(content: str, min_length: int = 100) -> bool:
        """Check if content is valid"""
        if not content or len(content.strip()) < min_length:
            return False
        
        # Check if content is mostly HTML tags
        import re
        text_content = re.sub(r'<[^>]+>', '', content)
        if len(text_content) < len(content) * 0.3:  # Less than 30% actual text
            return False
        
        return True
    
    @staticmethod
    def clean_content(content: str) -> str:
        """Clean and normalize content"""
        import re
        
        # Remove extra whitespace
        content = re.sub(r'\s+', ' ', content)
        
        # Remove HTML entities
        import html
        content = html.unescape(content)
        
        # Remove control characters
        content = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x84\x86-\x9f]', '', content)
        
        return content.strip()


# Utility functions
def setup_logging(level: str = "INFO", log_file: Optional[str] = None):
    """Setup logging configuration"""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            *([] if log_file is None else [logging.FileHandler(log_file)])
        ]
    )


def get_domain(url: str) -> str:
    """Extract domain from URL"""
    try:
        return urlparse(url).netloc
    except Exception:
        return ""
