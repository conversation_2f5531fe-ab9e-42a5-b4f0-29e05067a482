"""
Document class for HTML processing
"""

from typing import Dict, Any, Optional


class Document:
    """Simple document class to hold page content and metadata"""
    
    def __init__(self, page_content: str, metadata: Optional[Dict[str, Any]] = None):
        """
        Initialize document
        
        Args:
            page_content: The main content of the document
            metadata: Optional metadata dictionary
        """
        self.page_content = page_content
        self.metadata = metadata or {}
    
    def __str__(self) -> str:
        return f"Document(page_content='{self.page_content[:100]}...', metadata={self.metadata})"
    
    def __repr__(self) -> str:
        return self.__str__()
