"""
LangChain HTML Processor Core Module

A comprehensive HTML processing system based on <PERSON><PERSON>hai<PERSON> with support for:
- Multiple HTML loading strategies
- Intelligent content extraction
- Dynamic web page handling
- Batch processing capabilities
- Advanced error handling and caching
"""

from .html_loaders import (
    HTMLLoaderFactory,
    SmartHTMLLoader,
    BatchHTMLLoader,
    DynamicHTMLLoader,
    StructuredHTMLLoader,
    AuthenticatedHTMLLoader
)

from .content_extractor import (
    ContentExtractor,
    SmartContentExtractor,
    LayoutAnalyzer
)

from .processing_pipeline import (
    HTMLProcessingPipeline,
    PipelineConfig,
    ProcessingResult
)

from .utils import (
    EncodingDetector,
    ProxyRotator,
    CacheManager,
    RateLimiter,
    ErrorHandler
)

from .config import (
    Config,
    LoaderConfig,
    ExtractorConfig,
    PipelineConfig as ConfigPipeline
)

__version__ = "1.0.0"
__author__ = "LangChain HTML Processor Team"

__all__ = [
    # Loaders
    "HTMLLoaderFactory",
    "SmartHTMLLoader",
    "BatchHTMLLoader",
    "DynamicHTMLLoader",
    "StructuredHTMLLoader",
    "AuthenticatedHTMLLoader",
    
    # Extractors
    "ContentExtractor",
    "SmartContentExtractor",
    "LayoutAnalyzer",
    
    # Pipeline
    "HTMLProcessingPipeline",
    "PipelineConfig",
    "ProcessingResult",
    
    # Utils
    "EncodingDetector",
    "ProxyRotator", 
    "CacheManager",
    "RateLimiter",
    "ErrorHandler",
    
    # Config
    "Config",
    "LoaderConfig",
    "ExtractorConfig",
    "ConfigPipeline"
]
