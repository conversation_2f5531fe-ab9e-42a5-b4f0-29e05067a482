"""
Processing Pipeline for LangChain HTML Processor

Complete HTML processing pipeline with text splitting, embeddings, and vector storage
"""

import logging
from typing import List, Dict, Optional, Any, Union
from dataclasses import dataclass
from pathlib import Path

from langchain.docstore.document import Document

try:
    from langchain.text_splitter import RecursiveCharacterTextSplitter
except ImportError:
    RecursiveCharacterTextSplitter = None

try:
    from langchain.embeddings.base import Embeddings
except ImportError:
    Embeddings = None

try:
    from langchain.vectorstores.base import VectorStore
except ImportError:
    VectorStore = None

try:
    from langchain.embeddings import OpenAIEmbeddings
    from langchain.vectorstores import Chroma
except ImportError:
    OpenAIEmbeddings = None
    Chroma = None

from .html_loaders import HTMLLoaderFactory, BaseHTMLLoader
from .content_extractor import ContentExtractor, SmartContentExtractor
from .config import Config, PipelineConfig
from .utils import ContentValidator


@dataclass
class ProcessingResult:
    """Result of HTML processing pipeline"""
    documents: List[Document]
    chunks: List[Document]
    vector_store: Optional[VectorStore] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class HTMLProcessingPipeline:
    """Complete HTML processing pipeline"""
    
    def __init__(self, config: Optional[Config] = None):
        self.config = config or Config()
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Initialize components
        self.loader = self._create_loader()
        self.extractor = self._create_extractor()
        self.text_splitter = self._create_text_splitter()
        self.embeddings = self._create_embeddings()
        
    def _create_loader(self) -> BaseHTMLLoader:
        """Create HTML loader based on config"""
        return HTMLLoaderFactory.create_loader("smart", self.config.loader)
    
    def _create_extractor(self) -> ContentExtractor:
        """Create content extractor"""
        return SmartContentExtractor(self.config.extractor)
    
    def _create_text_splitter(self):
        """Create text splitter"""
        if RecursiveCharacterTextSplitter is None:
            # Simple fallback text splitter
            return self._create_simple_text_splitter()

        return RecursiveCharacterTextSplitter(
            chunk_size=self.config.pipeline.chunk_size,
            chunk_overlap=self.config.pipeline.chunk_overlap,
            separators=self.config.pipeline.separators,
            length_function=len
        )

    def _create_simple_text_splitter(self):
        """Create simple text splitter as fallback"""
        class SimpleTextSplitter:
            def __init__(self, chunk_size, chunk_overlap, separators):
                self.chunk_size = chunk_size
                self.chunk_overlap = chunk_overlap
                self.separators = separators

            def split_documents(self, documents):
                chunks = []
                for doc in documents:
                    text_chunks = self._split_text(doc.page_content)
                    for i, chunk_text in enumerate(text_chunks):
                        chunk_doc = Document(
                            page_content=chunk_text,
                            metadata={**doc.metadata, 'chunk_id': i}
                        )
                        chunks.append(chunk_doc)
                return chunks

            def _split_text(self, text):
                # Simple text splitting
                chunks = []
                start = 0
                while start < len(text):
                    end = start + self.chunk_size
                    if end >= len(text):
                        chunks.append(text[start:])
                        break

                    # Try to split at separator
                    chunk = text[start:end]
                    for sep in self.separators:
                        if sep in chunk:
                            split_pos = chunk.rfind(sep)
                            if split_pos > 0:
                                end = start + split_pos + len(sep)
                                break

                    chunks.append(text[start:end])
                    start = end - self.chunk_overlap

                return chunks

        return SimpleTextSplitter(
            chunk_size=self.config.pipeline.chunk_size,
            chunk_overlap=self.config.pipeline.chunk_overlap,
            separators=self.config.pipeline.separators
        )
    
    def _create_embeddings(self) -> Optional[Embeddings]:
        """Create embeddings model"""
        if not self.config.openai_api_key or not OpenAIEmbeddings:
            return None
        
        return OpenAIEmbeddings(
            model=self.config.pipeline.embedding_model,
            openai_api_key=self.config.openai_api_key
        )
    
    def process(
        self,
        source: Union[str, Path, List[str]],
        create_vector_store: bool = True
    ) -> ProcessingResult:
        """Process HTML content through complete pipeline"""
        
        self.logger.info(f"Starting HTML processing pipeline for: {source}")
        
        # Step 1: Load HTML content
        documents = self._load_content(source)
        if not documents:
            raise ValueError("No content could be loaded")
        
        # Step 2: Extract and enhance content
        enhanced_documents = self._extract_content(documents)
        
        # Step 3: Split into chunks
        chunks = self._split_documents(enhanced_documents)
        
        # Step 4: Create vector store (optional)
        vector_store = None
        if create_vector_store and self.embeddings:
            vector_store = self._create_vector_store(chunks)
        
        # Step 5: Compile metadata
        metadata = self._compile_metadata(documents, chunks, vector_store)
        
        result = ProcessingResult(
            documents=enhanced_documents,
            chunks=chunks,
            vector_store=vector_store,
            metadata=metadata
        )
        
        self.logger.info(f"Pipeline completed. Processed {len(documents)} documents into {len(chunks)} chunks")
        return result
    
    def _load_content(self, source: Union[str, Path, List[str]]) -> List[Document]:
        """Load HTML content using appropriate loader"""
        try:
            # Choose best loader for source type
            if isinstance(source, list) and len(source) > 1:
                loader = HTMLLoaderFactory.create_loader("batch", self.config.loader)
            else:
                loader = self.loader
            
            documents = loader.load(source)
            
            # Validate content
            valid_documents = []
            for doc in documents:
                if ContentValidator.is_valid_content(
                    doc.page_content, 
                    self.config.extractor.min_text_length
                ):
                    valid_documents.append(doc)
                else:
                    self.logger.warning(f"Skipping invalid content from {doc.metadata.get('source', 'unknown')}")
            
            return valid_documents
            
        except Exception as e:
            self.logger.error(f"Failed to load content: {e}")
            raise
    
    def _extract_content(self, documents: List[Document]) -> List[Document]:
        """Extract and enhance content from documents"""
        enhanced_documents = []
        
        for doc in documents:
            try:
                # Extract enhanced content
                extracted = self.extractor.extract(
                    doc.page_content, 
                    doc.metadata.get('source')
                )
                
                # Update document with extracted content
                doc.page_content = extracted.text
                
                # Enhance metadata
                doc.metadata.update({
                    'title': extracted.title,
                    'description': extracted.description,
                    'language': extracted.language,
                    'word_count': extracted.metadata.get('word_count', 0),
                    'char_count': extracted.metadata.get('char_count', 0),
                    'link_count': extracted.metadata.get('link_count', 0),
                    'image_count': extracted.metadata.get('image_count', 0),
                    'links': extracted.links if self.config.extractor.extract_links else [],
                    'images': extracted.images if self.config.extractor.extract_images else []
                })
                
                enhanced_documents.append(doc)
                
            except Exception as e:
                self.logger.error(f"Failed to extract content from document: {e}")
                # Keep original document if extraction fails
                enhanced_documents.append(doc)
        
        return enhanced_documents
    
    def _split_documents(self, documents: List[Document]) -> List[Document]:
        """Split documents into chunks"""
        try:
            chunks = self.text_splitter.split_documents(documents)
            
            # Add chunk metadata
            for i, chunk in enumerate(chunks):
                chunk.metadata.update({
                    'chunk_id': i,
                    'chunk_size': len(chunk.page_content),
                    'processing_pipeline': 'langchain_html_processor'
                })
            
            return chunks
            
        except Exception as e:
            self.logger.error(f"Failed to split documents: {e}")
            raise
    
    def _create_vector_store(self, chunks: List[Document]) -> Optional[VectorStore]:
        """Create vector store from chunks"""
        if not self.embeddings or not Chroma:
            self.logger.warning("Embeddings or Chroma not available, skipping vector store creation")
            return None
        
        try:
            # Create vector store directory
            vector_store_path = Path(self.config.pipeline.vector_store_path)
            vector_store_path.mkdir(parents=True, exist_ok=True)
            
            # Create vector store
            vector_store = Chroma.from_documents(
                documents=chunks,
                embedding=self.embeddings,
                persist_directory=str(vector_store_path)
            )
            
            # Persist the vector store
            vector_store.persist()
            
            self.logger.info(f"Created vector store with {len(chunks)} chunks")
            return vector_store
            
        except Exception as e:
            self.logger.error(f"Failed to create vector store: {e}")
            return None
    
    def _compile_metadata(
        self, 
        documents: List[Document], 
        chunks: List[Document], 
        vector_store: Optional[VectorStore]
    ) -> Dict[str, Any]:
        """Compile processing metadata"""
        
        total_chars = sum(len(doc.page_content) for doc in documents)
        total_words = sum(len(doc.page_content.split()) for doc in documents)
        
        languages = set()
        sources = set()
        
        for doc in documents:
            if doc.metadata.get('language'):
                languages.add(doc.metadata['language'])
            if doc.metadata.get('source'):
                sources.add(doc.metadata['source'])
        
        metadata = {
            'processing_timestamp': str(pd.Timestamp.now()) if 'pd' in globals() else str(datetime.now()),
            'total_documents': len(documents),
            'total_chunks': len(chunks),
            'total_characters': total_chars,
            'total_words': total_words,
            'average_chunk_size': total_chars / len(chunks) if chunks else 0,
            'languages_detected': list(languages),
            'sources_processed': list(sources),
            'has_vector_store': vector_store is not None,
            'config': self.config.to_dict()
        }
        
        return metadata
    
    def process_batch(
        self, 
        sources: List[Union[str, Path]], 
        create_vector_store: bool = True
    ) -> List[ProcessingResult]:
        """Process multiple sources in batch"""
        results = []
        
        for source in sources:
            try:
                result = self.process(source, create_vector_store)
                results.append(result)
            except Exception as e:
                self.logger.error(f"Failed to process {source}: {e}")
                # Create empty result for failed processing
                results.append(ProcessingResult(
                    documents=[],
                    chunks=[],
                    metadata={'error': str(e), 'source': str(source)}
                ))
        
        return results
    
    def create_retriever(self, vector_store: VectorStore, **kwargs):
        """Create retriever from vector store"""
        if not vector_store:
            raise ValueError("Vector store is required to create retriever")
        
        return vector_store.as_retriever(**kwargs)


# Convenience functions
def process_html_file(
    file_path: Union[str, Path], 
    config: Optional[Config] = None
) -> ProcessingResult:
    """Process single HTML file"""
    pipeline = HTMLProcessingPipeline(config)
    return pipeline.process(file_path)


def process_html_url(
    url: str, 
    config: Optional[Config] = None
) -> ProcessingResult:
    """Process single HTML URL"""
    pipeline = HTMLProcessingPipeline(config)
    return pipeline.process(url)


def process_html_urls(
    urls: List[str], 
    config: Optional[Config] = None
) -> ProcessingResult:
    """Process multiple HTML URLs"""
    pipeline = HTMLProcessingPipeline(config)
    return pipeline.process(urls)


# Import datetime for metadata
from datetime import datetime
