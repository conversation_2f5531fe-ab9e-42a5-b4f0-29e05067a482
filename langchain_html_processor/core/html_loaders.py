"""
Simplified HTML Loaders for Web Scraping Agent

Embedded version with core functionality needed for the agent.
"""

import asyncio
import logging
import re
from pathlib import Path
from typing import List, Dict, Optional, Union, Any
from abc import ABC, abstractmethod

import requests
from bs4 import BeautifulSoup

try:
    import aiohttp
except ImportError:
    aiohttp = None

try:
    from playwright.async_api import async_playwright
except ImportError:
    async_playwright = None

from .document import Document
from .config import LoaderConfig
from .utils import CacheManager, RateLimiter, ErrorHandler, ContentValidator


class BaseHTMLLoader(ABC):
    """Abstract base class for HTML loaders"""
    
    def __init__(self, config: Optional[LoaderConfig] = None):
        self.config = config or LoaderConfig()
        self.logger = logging.getLogger(self.__class__.__name__)
        self.cache_manager = CacheManager(
            cache_path=f"{getattr(self.config, 'cache_path', './.cache')}/html_cache.db",
            ttl=getattr(self.config, 'cache_ttl', 3600)
        )
        self.rate_limiter = RateLimiter(self.config.requests_per_second)
        self.error_handler = ErrorHandler(self.config.retries)
    
    @abstractmethod
    def load(self, source: Union[str, Path, List[str]]) -> List[Document]:
        """Load HTML content and return Document objects"""
        pass
    
    def _create_document(self, content: str, metadata: Dict[str, Any]) -> Document:
        """Create a Document object with cleaned content"""
        cleaned_content = ContentValidator.clean_content(content)
        return Document(page_content=cleaned_content, metadata=metadata)


class SmartHTMLLoader(BaseHTMLLoader):
    """Enhanced BSHTMLLoader with smart content extraction"""
    
    def __init__(self, config: Optional[LoaderConfig] = None):
        super().__init__(config)
        self.encoding_detector = EncodingDetector()
    
    def load(self, source: Union[str, Path]) -> List[Document]:
        """Load HTML from file or URL"""
        if isinstance(source, (str, Path)) and Path(source).exists():
            return self._load_from_file(Path(source))
        elif URLValidator.is_valid_url(str(source)):
            return self._load_from_url(str(source))
        else:
            raise ValueError(f"Invalid source: {source}")
    
    def _load_from_file(self, file_path: Path) -> List[Document]:
        """Load HTML from local file"""
        try:
            with open(file_path, 'rb') as f:
                raw_content = f.read()
            
            encoding = self.encoding_detector.detect_encoding(raw_content)
            content = self.encoding_detector.safe_decode(raw_content, encoding)
            
            extracted_content = self._extract_content(content)
            metadata = {
                'source': str(file_path),
                'encoding': encoding,
                'type': 'file'
            }
            
            return [self._create_document(extracted_content, metadata)]
            
        except Exception as e:
            self.logger.error(f"Failed to load file {file_path}: {e}")
            raise
    
    def _load_from_url(self, url: str) -> List[Document]:
        """Load HTML from URL"""
        # Check cache first
        cached_content = self.cache_manager.get(url)
        if cached_content:
            self.logger.info(f"Using cached content for {url}")
            metadata = {'source': url, 'type': 'url', 'cached': True}
            return [self._create_document(cached_content, metadata)]
        
        # Rate limiting
        self.rate_limiter.wait()
        
        try:
            session = self.error_handler.create_session()
            response = session.get(
                url,
                headers=self.config.headers,
                timeout=self.config.timeout
            )
            response.raise_for_status()
            
            # Detect encoding
            encoding = response.encoding or self.encoding_detector.detect_encoding(response.content)
            content = response.content.decode(encoding, errors='ignore')
            
            extracted_content = self._extract_content(content)
            
            # Cache the result
            self.cache_manager.set(url, extracted_content)
            
            metadata = {
                'source': url,
                'encoding': encoding,
                'type': 'url',
                'status_code': response.status_code
            }
            
            return [self._create_document(extracted_content, metadata)]
            
        except Exception as e:
            self.logger.error(f"Failed to load URL {url}: {e}")
            raise
    
    def _extract_content(self, html_content: str) -> str:
        """Extract main content from HTML"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Remove unwanted elements
        remove_selectors = [
            'script', 'style', 'nav', 'header', 'footer', 
            '.advertisement', '.ads', '.sidebar', '.comments'
        ]
        for selector in remove_selectors:
            for element in soup.select(selector):
                element.decompose()
        
        # Try to find main content
        content_selectors = [
            'article', '.post-content', '.article-body', 'main', '[role="main"]'
        ]
        
        for selector in content_selectors:
            element = soup.select_one(selector)
            if element and len(element.get_text(strip=True)) > 500:
                return element.get_text(separator='\n', strip=True)
        
        # Fallback to body content
        body = soup.find('body')
        if body:
            return body.get_text(separator='\n', strip=True)
        
        return soup.get_text(separator='\n', strip=True)


class StructuredHTMLLoader(BaseHTMLLoader):
    """Structure-preserving HTML loader using Unstructured"""
    
    def load(self, source: Union[str, Path]) -> List[Document]:
        """Load HTML with structure preservation"""
        if LangChainUnstructuredHTMLLoader is None:
            raise ImportError("UnstructuredHTMLLoader not available")
        
        try:
            loader = LangChainUnstructuredHTMLLoader(
                str(source), 
                mode="elements"
            )
            docs = loader.load()
            
            # Enhance metadata
            for doc in docs:
                doc.metadata.update({
                    'loader_type': 'structured',
                    'preserves_structure': True
                })
            
            return docs
            
        except Exception as e:
            self.logger.error(f"Failed to load with StructuredHTMLLoader: {e}")
            # Fallback to SmartHTMLLoader
            fallback_loader = SmartHTMLLoader(self.config)
            return fallback_loader.load(source)


class DynamicHTMLLoader(BaseHTMLLoader):
    """Dynamic HTML loader with JavaScript rendering"""
    
    def __init__(self, config: Optional[LoaderConfig] = None):
        super().__init__(config)
        self.proxy_rotator = ProxyRotator(self.config.proxies) if self.config.proxies else None
    
    async def load_async(self, urls: List[str]) -> List[Document]:
        """Load multiple URLs with JavaScript rendering"""
        if async_playwright is None:
            raise ImportError("Playwright is required for dynamic loading. Install with: pip install playwright")

        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)

            try:
                documents = []
                for url in urls:
                    doc = await self._load_single_url(browser, url)
                    if doc:
                        documents.append(doc)

                return documents

            finally:
                await browser.close()
    
    def load(self, source: Union[str, List[str]]) -> List[Document]:
        """Load HTML with JavaScript rendering (sync wrapper)"""
        urls = [source] if isinstance(source, str) else source
        return asyncio.run(self.load_async(urls))
    
    async def _load_single_url(self, browser, url: str) -> Optional[Document]:
        """Load single URL with browser"""
        try:
            # Rate limiting
            self.rate_limiter.wait()
            
            context = await browser.new_context(
                user_agent=self.config.user_agent,
                extra_http_headers=self.config.headers
            )
            
            page = await context.new_page()
            
            # Navigate and wait for content
            await page.goto(url, timeout=self.config.timeout * 1000)
            await page.wait_for_timeout(self.config.wait_for_js * 1000)
            
            # Get content
            content = await page.content()
            title = await page.title()
            
            await context.close()
            
            # Extract text content
            extracted_content = self._extract_content(content)
            
            metadata = {
                'source': url,
                'title': title,
                'type': 'dynamic_url',
                'loader_type': 'dynamic'
            }
            
            return self._create_document(extracted_content, metadata)
            
        except Exception as e:
            self.logger.error(f"Failed to load dynamic content from {url}: {e}")
            return None
    
    def _extract_content(self, html_content: str) -> str:
        """Extract content from rendered HTML"""
        soup = BeautifulSoup(html_content, 'html.parser')

        # Remove scripts and styles
        for script in soup(["script", "style"]):
            script.decompose()

        return soup.get_text(separator='\n', strip=True)


class AuthenticatedHTMLLoader(DynamicHTMLLoader):
    """HTML loader with authentication support for login-required websites"""

    def __init__(self, config: Optional[LoaderConfig] = None):
        super().__init__(config)
        self.login_selectors = {
            'username_field': ['input[name="email"]', 'input[name="username"]', 'input[type="email"]', '#email', '#username'],
            'password_field': ['input[name="password"]', 'input[type="password"]', '#password'],
            'login_button': ['button[type="submit"]', 'input[type="submit"]', '.login-button', '#login-button', 'button:contains("Login")', 'button:contains("Sign In")'],
            'login_form': ['form', '.login-form', '#login-form']
        }

    async def login(self, browser, username: str, password: str, login_url: str,
                   custom_selectors: Optional[Dict[str, List[str]]] = None) -> bool:
        """Perform login on the website"""
        try:
            selectors = {**self.login_selectors, **(custom_selectors or {})}

            context = await browser.new_context(
                user_agent=self.config.user_agent,
                extra_http_headers=self.config.headers,
                viewport={'width': 1920, 'height': 1080},
                locale='en-US',
                timezone_id='America/New_York'
            )

            page = await context.new_page()

            # Navigate to login page
            await page.goto(login_url, timeout=self.config.timeout * 1000)
            await page.wait_for_timeout(2000)  # Wait for page to load

            # Find and fill username field
            username_filled = False
            for selector in selectors['username_field']:
                try:
                    await page.wait_for_selector(selector, timeout=5000)
                    await page.fill(selector, username)
                    username_filled = True
                    self.logger.info(f"Username filled using selector: {selector}")
                    break
                except Exception:
                    continue

            if not username_filled:
                self.logger.error("Could not find username field")
                await context.close()
                return False

            # Find and fill password field
            password_filled = False
            for selector in selectors['password_field']:
                try:
                    await page.wait_for_selector(selector, timeout=5000)
                    await page.fill(selector, password)
                    password_filled = True
                    self.logger.info(f"Password filled using selector: {selector}")
                    break
                except Exception:
                    continue

            if not password_filled:
                self.logger.error("Could not find password field")
                await context.close()
                return False

            # Submit the form
            login_submitted = False
            for selector in selectors['login_button']:
                try:
                    await page.wait_for_selector(selector, timeout=5000)
                    await page.click(selector)
                    login_submitted = True
                    self.logger.info(f"Login submitted using selector: {selector}")
                    break
                except Exception:
                    continue

            if not login_submitted:
                # Try submitting the form directly
                try:
                    for form_selector in selectors['login_form']:
                        try:
                            await page.wait_for_selector(form_selector, timeout=5000)
                            await page.evaluate(f'document.querySelector("{form_selector}").submit()')
                            login_submitted = True
                            break
                        except Exception:
                            continue
                except Exception:
                    pass

            if not login_submitted:
                self.logger.error("Could not submit login form")
                await context.close()
                return False

            # Wait for navigation after login
            try:
                await page.wait_for_navigation(timeout=10000)
            except Exception:
                # Sometimes there's no navigation, just wait a bit
                await page.wait_for_timeout(3000)

            # Check if login was successful (look for common indicators)
            current_url = page.url
            page_content = await page.content()

            # Log detailed information for debugging
            self.logger.info(f"After login - Current URL: {current_url}")
            self.logger.info(f"Page content length: {len(page_content)}")

            # Simple checks for login success
            success_indicators = [
                'dashboard', 'profile', 'logout', 'account', 'welcome',
                'member', 'user', 'settings', 'products', 'catalog'
            ]

            failure_indicators = [
                'error', 'invalid', 'incorrect', 'failed', 'try again',
                'please enter', 'required field'
            ]

            content_lower = page_content.lower()
            url_lower = current_url.lower()

            # Check URL for success indicators
            url_success = any(indicator in url_lower for indicator in success_indicators)

            # Check content for failure indicators
            content_failure = any(indicator in content_lower for indicator in failure_indicators)

            # Log what we found
            found_success = [ind for ind in success_indicators if ind in content_lower or ind in url_lower]
            found_failure = [ind for ind in failure_indicators if ind in content_lower]

            self.logger.info(f"Found success indicators: {found_success}")
            self.logger.info(f"Found failure indicators: {found_failure}")

            # Store the context for reuse
            self._auth_context = context
            self._auth_page = page

            # More lenient success check - if we're not on login page and no obvious errors
            login_page_indicators = ['login', 'sign in', 'sign-in']
            still_on_login = any(indicator in url_lower for indicator in login_page_indicators)

            if not still_on_login and not content_failure:
                self.logger.info("Login appears successful - redirected away from login page")
                return True
            elif url_success and not content_failure:
                self.logger.info("Login appears successful - found success indicators")
                return True
            elif found_success and not found_failure:
                self.logger.info("Login appears successful - success indicators found")
                return True
            else:
                self.logger.warning(f"Login may have failed - still on login: {still_on_login}, failures: {found_failure}")
                # Save page content for debugging
                with open('debug_login_page.html', 'w', encoding='utf-8') as f:
                    f.write(page_content)
                self.logger.info("Saved login page content to debug_login_page.html for inspection")
                return False

        except Exception as e:
            self.logger.error(f"Login failed with error: {e}")
            if 'context' in locals():
                await context.close()
            return False

    async def load_with_auth(self, target_url: str, username: str, password: str,
                           login_url: str, custom_selectors: Optional[Dict[str, List[str]]] = None) -> List[Document]:
        """Load content from authenticated URL"""
        if async_playwright is None:
            raise ImportError("Playwright is required for authenticated loading. Install with: pip install playwright")

        async with async_playwright() as p:
            browser = await p.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )

            try:
                # Check if this is Restaurant Depot and use specialized login
                if "restaurantdepot.com" in login_url.lower():
                    self.logger.info("Using Restaurant Depot specialized login")
                    login_success = await self.login_restaurant_depot(browser, username, password)
                else:
                    # Use general login method
                    login_success = await self.login(browser, username, password, login_url, custom_selectors)

                if not login_success:
                    self.logger.error("Login failed, cannot proceed with content loading")
                    return []

                # Use the authenticated context to load target content
                page = self._auth_page

                # Navigate to target URL
                await page.goto(target_url, timeout=self.config.timeout * 1000)
                await page.wait_for_timeout(self.config.wait_for_js * 1000)

                # Get content
                content = await page.content()
                title = await page.title()

                # Extract text content
                extracted_content = self._extract_content(content)

                metadata = {
                    'source': target_url,
                    'title': title,
                    'type': 'authenticated_url',
                    'loader_type': 'authenticated',
                    'login_url': login_url,
                    'authenticated': True
                }

                return [self._create_document(extracted_content, metadata)]

            except Exception as e:
                self.logger.error(f"Failed to load authenticated content from {target_url}: {e}")
                return []
            finally:
                if hasattr(self, '_auth_context'):
                    await self._auth_context.close()
                await browser.close()

    async def login_restaurant_depot(self, browser, username: str, password: str) -> bool:
        """Specialized login method for Restaurant Depot"""
        try:
            context = await browser.new_context(
                user_agent=self.config.user_agent,
                extra_http_headers={
                    **self.config.headers,
                    "Referer": "https://member.restaurantdepot.com/",
                    "Origin": "https://member.restaurantdepot.com"
                },
                viewport={'width': 1920, 'height': 1080},
                locale='en-US',
                timezone_id='America/New_York'
            )

            page = await context.new_page()

            # Step 1: Get the login page and extract form_key
            self.logger.info("Getting Restaurant Depot login page...")
            await page.goto("https://member.restaurantdepot.com/", timeout=self.config.timeout * 1000)
            await page.wait_for_timeout(3000)

            # Extract form_key
            form_key = None
            try:
                # Try to find form_key element
                form_key_elements = await page.query_selector_all('input[name="form_key"]')
                if form_key_elements:
                    form_key = await form_key_elements[0].get_attribute('value')
                    self.logger.info(f"Found form_key: {form_key[:20]}...")
                else:
                    # Try alternative method - get from page content
                    page_content = await page.content()
                    import re
                    form_key_match = re.search(r'name="form_key"[^>]*value="([^"]+)"', page_content)
                    if form_key_match:
                        form_key = form_key_match.group(1)
                        self.logger.info(f"Found form_key via regex: {form_key[:20]}...")
                    else:
                        self.logger.error("Could not find form_key in page")
                        await context.close()
                        return False
            except Exception as e:
                self.logger.error(f"Error extracting form_key: {e}")
                await context.close()
                return False

            if not form_key:
                self.logger.error("form_key is empty")
                await context.close()
                return False

            # Step 2: Perform login via POST request
            self.logger.info("Performing Restaurant Depot login...")

            # Use page.evaluate to perform the POST request
            login_script = f"""
            async () => {{
                const formData = new FormData();
                formData.append('form_key', '{form_key}');
                formData.append('login[username]', '{username}');
                formData.append('login[password]', '{password}');

                const response = await fetch('https://member.restaurantdepot.com/customer/account/loginPost/', {{
                    method: 'POST',
                    body: formData,
                    headers: {{
                        'Referer': 'https://member.restaurantdepot.com/',
                        'Origin': 'https://member.restaurantdepot.com'
                    }}
                }});

                return {{
                    status: response.status,
                    url: response.url,
                    text: await response.text()
                }};
            }}
            """

            login_result = await page.evaluate(login_script)
            self.logger.info(f"Login POST response: status={login_result['status']}, url={login_result['url']}")

            # Check if login was successful
            if "/products" in login_result.get('text', '') or login_result['status'] == 200:
                self.logger.info("Login appears successful")

                # Step 3: Set user location (California, region 19)
                self.logger.info("Setting user location...")
                location_script = f"""
                async () => {{
                    const response = await fetch('https://member.restaurantdepot.com/location/save/userlocation?form_key={form_key}&warehouse_state=CA&region_id=19', {{
                        method: 'POST',
                        headers: {{
                            'Referer': 'https://member.restaurantdepot.com/',
                            'Origin': 'https://member.restaurantdepot.com'
                        }}
                    }});
                    return {{
                        status: response.status,
                        text: await response.text()
                    }};
                }}
                """

                location_result = await page.evaluate(location_script)
                self.logger.info(f"Location setup: status={location_result['status']}")

                # Store the context for reuse
                self._auth_context = context
                self._auth_page = page

                return True
            else:
                self.logger.error("Login failed - products page not found in response")
                await context.close()
                return False

        except Exception as e:
            self.logger.error(f"Restaurant Depot login failed with error: {e}")
            if 'context' in locals():
                await context.close()
            return False


class BatchHTMLLoader(BaseHTMLLoader):
    """Async batch HTML loader for high-performance processing"""

    def __init__(self, config: Optional[LoaderConfig] = None):
        super().__init__(config)
        self.proxy_rotator = ProxyRotator(self.config.proxies) if self.config.proxies else None

    async def load_async(self, urls: List[str]) -> List[Document]:
        """Load multiple URLs asynchronously"""
        if aiohttp is None:
            raise ImportError("aiohttp is required for batch loading. Install with: pip install aiohttp")

        connector = aiohttp.TCPConnector(limit=10)
        timeout = aiohttp.ClientTimeout(total=self.config.timeout)

        async with aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers=self.config.headers
        ) as session:

            tasks = []
            for url in urls:
                task = self._fetch_url(session, url)
                tasks.append(task)

            results = await asyncio.gather(*tasks, return_exceptions=True)

            documents = []
            for url, result in zip(urls, results):
                if isinstance(result, Exception):
                    self.logger.error(f"Failed to fetch {url}: {result}")
                    continue

                if result:
                    content, metadata = result
                    doc = self._create_document(content, metadata)
                    documents.append(doc)

            return documents

    def load(self, source: List[str]) -> List[Document]:
        """Load multiple URLs (sync wrapper)"""
        return asyncio.run(self.load_async(source))

    async def _fetch_url(self, session: aiohttp.ClientSession, url: str) -> Optional[tuple]:
        """Fetch single URL asynchronously"""
        try:
            # Rate limiting (simplified for async)
            await asyncio.sleep(1.0 / self.config.requests_per_second)

            proxy = None
            if self.proxy_rotator:
                proxy_dict = self.proxy_rotator.get_proxy()
                proxy = proxy_dict.get('http') if proxy_dict else None

            async with session.get(url, proxy=proxy) as response:
                if response.status == 200:
                    content = await response.text()

                    # Extract main content
                    extracted_content = self._extract_content(content)

                    metadata = {
                        'source': url,
                        'type': 'batch_url',
                        'status_code': response.status,
                        'loader_type': 'batch'
                    }

                    return extracted_content, metadata
                else:
                    self.logger.warning(f"HTTP {response.status} for {url}")
                    return None

        except Exception as e:
            self.logger.error(f"Error fetching {url}: {e}")
            return None

    def _extract_content(self, html_content: str) -> str:
        """Extract content from HTML"""
        soup = BeautifulSoup(html_content, 'html.parser')

        # Remove unwanted elements
        for element in soup(["script", "style", "nav", "header", "footer"]):
            element.decompose()

        # Try to find main content
        main_content = soup.find('main') or soup.find('article') or soup.find('body')
        if main_content:
            return main_content.get_text(separator='\n', strip=True)

        return soup.get_text(separator='\n', strip=True)


class HTMLLoaderFactory:
    """Factory class for creating appropriate HTML loaders"""

    @staticmethod
    def create_loader(
        loader_type: str = "smart",
        config: Optional[LoaderConfig] = None
    ) -> BaseHTMLLoader:
        """Create HTML loader based on type"""

        loaders = {
            "smart": SmartHTMLLoader,
            "structured": StructuredHTMLLoader,
            "dynamic": DynamicHTMLLoader,
            "batch": BatchHTMLLoader,
            "authenticated": AuthenticatedHTMLLoader
        }

        if loader_type not in loaders:
            raise ValueError(f"Unknown loader type: {loader_type}")

        return loaders[loader_type](config)

    @staticmethod
    def get_best_loader(
        source: Union[str, List[str]],
        config: Optional[LoaderConfig] = None
    ) -> BaseHTMLLoader:
        """Get the best loader for the given source"""

        if isinstance(source, list):
            if len(source) > 5:
                return BatchHTMLLoader(config)
            else:
                return DynamicHTMLLoader(config)

        elif isinstance(source, str):
            if URLValidator.is_valid_url(source):
                # Check if URL likely needs JS rendering
                js_indicators = [
                    'spa', 'react', 'angular', 'vue', 'app',
                    'dynamic', 'ajax', 'api'
                ]
                if any(indicator in source.lower() for indicator in js_indicators):
                    return DynamicHTMLLoader(config)
                else:
                    return SmartHTMLLoader(config)
            else:
                return SmartHTMLLoader(config)

        return SmartHTMLLoader(config)


# Convenience functions for backward compatibility
def load_html_file(file_path: Union[str, Path], config: Optional[LoaderConfig] = None) -> List[Document]:
    """Load HTML from file"""
    loader = SmartHTMLLoader(config)
    return loader.load(file_path)


def load_html_url(url: str, config: Optional[LoaderConfig] = None) -> List[Document]:
    """Load HTML from URL"""
    loader = SmartHTMLLoader(config)
    return loader.load(url)


def load_html_urls(urls: List[str], config: Optional[LoaderConfig] = None) -> List[Document]:
    """Load HTML from multiple URLs"""
    loader = BatchHTMLLoader(config)
    return loader.load(urls)


def load_dynamic_html(url: str, config: Optional[LoaderConfig] = None) -> List[Document]:
    """Load dynamic HTML with JS rendering"""
    loader = DynamicHTMLLoader(config)
    return loader.load(url)
