"""
Configuration management for LangChain HTML Processor
"""

import os
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from pathlib import Path


@dataclass
class LoaderConfig:
    """Configuration for HTML loaders"""
    # Basic settings
    encoding: str = "utf-8"
    timeout: int = 30
    retries: int = 3
    
    # Rate limiting
    requests_per_second: float = 2.0
    delay_between_requests: float = 0.5
    
    # Headers and user agent
    user_agent: str = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    headers: Dict[str, str] = field(default_factory=lambda: {
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5",
        "Accept-Encoding": "gzip, deflate",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1"
    })
    
    # Proxy settings
    proxies: List[str] = field(default_factory=list)
    rotate_proxy: bool = False
    
    # JavaScript rendering
    enable_js: bool = True
    wait_for_js: float = 2.0
    
    # Content filtering
    min_content_length: int = 100
    max_content_length: int = 1000000


@dataclass
class ExtractorConfig:
    """Configuration for content extraction"""
    # Content selectors (in priority order)
    content_selectors: List[str] = field(default_factory=lambda: [
        'article',
        '.post-content',
        '.article-body',
        '.content',
        'main',
        '[role="main"]',
        '.entry-content',
        '#content'
    ])
    
    # Elements to remove
    remove_selectors: List[str] = field(default_factory=lambda: [
        'script',
        'style',
        'nav',
        'header',
        'footer',
        '.advertisement',
        '.ads',
        '.sidebar',
        '.comments'
    ])
    
    # Text processing
    min_text_length: int = 50
    preserve_formatting: bool = True
    extract_links: bool = True
    extract_images: bool = True
    
    # Language detection
    detect_language: bool = True
    supported_languages: List[str] = field(default_factory=lambda: [
        'en', 'zh', 'ja', 'ko', 'es', 'fr', 'de', 'ru'
    ])


@dataclass
class PipelineConfig:
    """Configuration for processing pipeline"""
    # Text splitting
    chunk_size: int = 1500
    chunk_overlap: int = 200
    separators: List[str] = field(default_factory=lambda: [
        "\n\n", "\n", "。", "！", "？", ".", "!", "?"
    ])
    
    # Embeddings
    embedding_model: str = "text-embedding-ada-002"
    embedding_batch_size: int = 100
    
    # Vector store
    vector_store_type: str = "chroma"
    vector_store_path: str = "./vector_store"
    
    # Caching
    enable_cache: bool = True
    cache_ttl: int = 3600  # 1 hour
    cache_path: str = "./.cache"
    
    # Processing options
    parallel_processing: bool = True
    max_workers: int = 4
    batch_size: int = 10


@dataclass
class Config:
    """Main configuration class"""
    # Sub-configurations
    loader: LoaderConfig = field(default_factory=LoaderConfig)
    extractor: ExtractorConfig = field(default_factory=ExtractorConfig)
    pipeline: PipelineConfig = field(default_factory=PipelineConfig)
    
    # API keys
    openai_api_key: Optional[str] = "********************************************************************************************************************************************************************"
    
    # Logging
    log_level: str = "INFO"
    log_file: Optional[str] = None
    
    # Debug mode
    debug: bool = False
    
    def __post_init__(self):
        """Load environment variables"""
        self.openai_api_key = self.openai_api_key or os.getenv("OPENAI_API_KEY")
        
        # Create necessary directories
        Path(self.pipeline.cache_path).mkdir(parents=True, exist_ok=True)
        Path(self.pipeline.vector_store_path).mkdir(parents=True, exist_ok=True)
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'Config':
        """Create config from dictionary"""
        loader_config = LoaderConfig(**config_dict.get('loader', {}))
        extractor_config = ExtractorConfig(**config_dict.get('extractor', {}))
        pipeline_config = PipelineConfig(**config_dict.get('pipeline', {}))
        
        return cls(
            loader=loader_config,
            extractor=extractor_config,
            pipeline=pipeline_config,
            **{k: v for k, v in config_dict.items() 
               if k not in ['loader', 'extractor', 'pipeline']}
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary"""
        return {
            'loader': self.loader.__dict__,
            'extractor': self.extractor.__dict__,
            'pipeline': self.pipeline.__dict__,
            'openai_api_key': self.openai_api_key,
            'log_level': self.log_level,
            'log_file': self.log_file,
            'debug': self.debug
        }


# Default configuration instance
default_config = Config()
