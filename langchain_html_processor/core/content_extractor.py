"""
Content Extraction for LangChain HTML Processor

Advanced content extraction with intelligent parsing and layout analysis
"""

import re
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from bs4 import BeautifulSoup, Tag, NavigableString
from langchain.docstore.document import Document

try:
    from langdetect import detect, LangDetectException
except ImportError:
    detect = None
    LangDetectException = Exception

from .config import ExtractorConfig
from .utils import ContentValidator


@dataclass
class ExtractedContent:
    """Container for extracted content with metadata"""
    text: str
    title: Optional[str] = None
    description: Optional[str] = None
    language: Optional[str] = None
    links: List[Dict[str, str]] = None
    images: List[Dict[str, str]] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.links is None:
            self.links = []
        if self.images is None:
            self.images = []
        if self.metadata is None:
            self.metadata = {}


class ContentExtractor:
    """Basic content extractor with configurable selectors"""
    
    def __init__(self, config: Optional[ExtractorConfig] = None):
        self.config = config or ExtractorConfig()
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def extract(self, html_content: str, url: Optional[str] = None) -> ExtractedContent:
        """Extract content from HTML"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Clean HTML first
        self._clean_html(soup)
        
        # Extract different components
        title = self._extract_title(soup)
        description = self._extract_description(soup)
        main_text = self._extract_main_text(soup)
        links = self._extract_links(soup, url) if self.config.extract_links else []
        images = self._extract_images(soup, url) if self.config.extract_images else []
        language = self._detect_language(main_text) if self.config.detect_language else None
        
        # Additional metadata
        metadata = {
            'word_count': len(main_text.split()),
            'char_count': len(main_text),
            'link_count': len(links),
            'image_count': len(images)
        }
        
        return ExtractedContent(
            text=main_text,
            title=title,
            description=description,
            language=language,
            links=links,
            images=images,
            metadata=metadata
        )
    
    def _clean_html(self, soup: BeautifulSoup):
        """Remove unwanted elements from HTML"""
        # Remove elements by selector
        for selector in self.config.remove_selectors:
            for element in soup.select(selector):
                element.decompose()
        
        # Remove empty elements
        for element in soup.find_all():
            if not element.get_text(strip=True) and not element.find('img'):
                element.decompose()
    
    def _extract_title(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract page title"""
        # Try different title sources
        title_sources = [
            soup.find('title'),
            soup.find('h1'),
            soup.select_one('.title'),
            soup.select_one('[property="og:title"]'),
            soup.select_one('[name="twitter:title"]')
        ]
        
        for source in title_sources:
            if source:
                title = source.get('content') if source.has_attr('content') else source.get_text()
                if title and title.strip():
                    return title.strip()
        
        return None
    
    def _extract_description(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract page description"""
        # Try meta description first
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        if meta_desc and meta_desc.get('content'):
            return meta_desc['content'].strip()
        
        # Try Open Graph description
        og_desc = soup.find('meta', attrs={'property': 'og:description'})
        if og_desc and og_desc.get('content'):
            return og_desc['content'].strip()
        
        # Try first paragraph
        first_p = soup.find('p')
        if first_p:
            text = first_p.get_text(strip=True)
            if len(text) > 50:
                return text[:200] + '...' if len(text) > 200 else text
        
        return None
    
    def _extract_main_text(self, soup: BeautifulSoup) -> str:
        """Extract main text content"""
        # Try content selectors in order
        for selector in self.config.content_selectors:
            element = soup.select_one(selector)
            if element:
                text = element.get_text(separator='\n', strip=True)
                if len(text) > self.config.min_text_length:
                    return text
        
        # Fallback to body
        body = soup.find('body')
        if body:
            return body.get_text(separator='\n', strip=True)
        
        return soup.get_text(separator='\n', strip=True)
    
    def _extract_links(self, soup: BeautifulSoup, base_url: Optional[str] = None) -> List[Dict[str, str]]:
        """Extract links from HTML"""
        links = []
        
        for link in soup.find_all('a', href=True):
            href = link['href']
            text = link.get_text(strip=True)
            
            # Skip empty links or anchors
            if not href or href.startswith('#'):
                continue
            
            # Convert relative URLs to absolute
            if base_url and not href.startswith(('http://', 'https://')):
                from urllib.parse import urljoin
                href = urljoin(base_url, href)
            
            links.append({
                'url': href,
                'text': text,
                'title': link.get('title', '')
            })
        
        return links
    
    def _extract_images(self, soup: BeautifulSoup, base_url: Optional[str] = None) -> List[Dict[str, str]]:
        """Extract images from HTML"""
        images = []
        
        for img in soup.find_all('img', src=True):
            src = img['src']
            
            # Convert relative URLs to absolute
            if base_url and not src.startswith(('http://', 'https://')):
                from urllib.parse import urljoin
                src = urljoin(base_url, src)
            
            images.append({
                'url': src,
                'alt': img.get('alt', ''),
                'title': img.get('title', ''),
                'width': img.get('width', ''),
                'height': img.get('height', '')
            })
        
        return images
    
    def _detect_language(self, text: str) -> Optional[str]:
        """Detect text language"""
        if detect is None or len(text) < 50:
            return None

        try:
            lang = detect(text)
            if lang in self.config.supported_languages:
                return lang
        except (LangDetectException, Exception):
            pass

        return None


class SmartContentExtractor(ContentExtractor):
    """Advanced content extractor with density-based analysis"""
    
    def _extract_main_text(self, soup: BeautifulSoup) -> str:
        """Extract main text using content density analysis"""
        # First try parent class method
        content = super()._extract_main_text(soup)
        
        if len(content) > self.config.min_text_length:
            return content
        
        # Use density-based extraction as fallback
        return self._extract_by_density(soup)
    
    def _extract_by_density(self, soup: BeautifulSoup) -> str:
        """Extract content based on text density"""
        candidates = []
        
        # Analyze all potential content containers
        for element in soup.find_all(['div', 'article', 'section', 'main']):
            if not element.get_text(strip=True):
                continue
            
            text_length = len(element.get_text())
            html_length = len(str(element))
            
            # Calculate text density
            density = text_length / html_length if html_length > 0 else 0
            
            # Score based on density and text length
            score = density * min(text_length / 1000, 1.0)
            
            candidates.append((score, element, text_length))
        
        # Sort by score and return best candidate
        candidates.sort(key=lambda x: x[0], reverse=True)
        
        if candidates:
            best_element = candidates[0][1]
            return best_element.get_text(separator='\n', strip=True)
        
        return soup.get_text(separator='\n', strip=True)


class LayoutAnalyzer:
    """Analyze HTML layout for better content extraction"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def analyze_layout(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Analyze HTML layout structure"""
        analysis = {
            'has_header': bool(soup.find(['header', '.header', '#header'])),
            'has_footer': bool(soup.find(['footer', '.footer', '#footer'])),
            'has_sidebar': bool(soup.find(['.sidebar', '.side', '#sidebar'])),
            'has_navigation': bool(soup.find(['nav', '.nav', '.navigation'])),
            'main_content_area': self._find_main_content_area(soup),
            'content_blocks': self._identify_content_blocks(soup)
        }
        
        return analysis
    
    def _find_main_content_area(self, soup: BeautifulSoup) -> Optional[str]:
        """Find the main content area selector"""
        main_selectors = [
            'main', '[role="main"]', '.main', '#main',
            '.content', '#content', '.post', '.article'
        ]
        
        for selector in main_selectors:
            element = soup.select_one(selector)
            if element and len(element.get_text(strip=True)) > 200:
                return selector
        
        return None
    
    def _identify_content_blocks(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """Identify different content blocks"""
        blocks = []
        
        for element in soup.find_all(['div', 'section', 'article']):
            text = element.get_text(strip=True)
            if len(text) < 50:
                continue
            
            block_info = {
                'tag': element.name,
                'classes': element.get('class', []),
                'id': element.get('id', ''),
                'text_length': len(text),
                'child_count': len(element.find_all()),
                'type': self._classify_block(element)
            }
            
            blocks.append(block_info)
        
        return blocks
    
    def _classify_block(self, element: Tag) -> str:
        """Classify content block type"""
        classes = ' '.join(element.get('class', [])).lower()
        element_id = element.get('id', '').lower()
        
        # Classification rules
        if any(keyword in classes + element_id for keyword in ['nav', 'menu']):
            return 'navigation'
        elif any(keyword in classes + element_id for keyword in ['sidebar', 'side']):
            return 'sidebar'
        elif any(keyword in classes + element_id for keyword in ['footer']):
            return 'footer'
        elif any(keyword in classes + element_id for keyword in ['header']):
            return 'header'
        elif any(keyword in classes + element_id for keyword in ['content', 'main', 'article', 'post']):
            return 'main_content'
        elif any(keyword in classes + element_id for keyword in ['ad', 'advertisement']):
            return 'advertisement'
        else:
            return 'unknown'
