"""
Advanced usage examples for LangChain HTML Processor
"""

import asyncio
import time
from pathlib import Path
from typing import List

from langchain_html_processor.core import (
    HTMLLoaderFactory,
    HTMLProcessingPipeline,
    SmartContentExtractor,
    LayoutAnalyzer,
    Config,
    LoaderConfig,
    ExtractorConfig
)


def example_1_dynamic_content():
    """Example 1: Loading dynamic content with JavaScript"""
    print("=== Example 1: Dynamic Content Loading ===")
    
    # URLs that require JavaScript rendering
    dynamic_urls = [
        "https://quotes.toscrape.com/js/",  # JavaScript-rendered quotes
    ]
    
    try:
        # Use dynamic loader for JS-heavy sites
        loader = HTMLLoaderFactory.create_loader("dynamic")
        
        for url in dynamic_urls:
            print(f"Loading dynamic content from: {url}")
            documents = loader.load(url)
            
            if documents:
                doc = documents[0]
                print(f"Content length: {len(doc.page_content)}")
                print(f"Title: {doc.metadata.get('title', 'No title')}")
                print(f"Content preview: {doc.page_content[:200]}...")
            else:
                print("No content loaded")
                
    except Exception as e:
        print(f"Dynamic loading failed: {e}")
        print("Note: This requires <PERSON><PERSON> to be installed and configured")


async def example_2_async_batch_processing():
    """Example 2: Asynchronous batch processing"""
    print("\n=== Example 2: Async Batch Processing ===")
    
    urls = [
        "https://httpbin.org/html",
        "https://httpbin.org/json",
        "https://httpbin.org/xml"
    ]
    
    try:
        loader = HTMLLoaderFactory.create_loader("batch")
        
        # Time the batch processing
        start_time = time.time()
        documents = await loader.load_async(urls)
        end_time = time.time()
        
        print(f"Processed {len(documents)} URLs in {end_time - start_time:.2f} seconds")
        
        for doc in documents:
            source = doc.metadata.get('source', 'unknown')
            status = doc.metadata.get('status_code', 'unknown')
            print(f"- {source}: Status {status}, Length {len(doc.page_content)}")
            
    except Exception as e:
        print(f"Async batch processing failed: {e}")


def example_3_content_analysis():
    """Example 3: Advanced content analysis"""
    print("\n=== Example 3: Content Analysis ===")
    
    # Create complex HTML for analysis
    complex_html = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <title>Complex Web Page</title>
        <meta name="description" content="A complex page for testing content analysis">
    </head>
    <body>
        <header class="site-header">
            <nav class="main-nav">
                <ul>
                    <li><a href="/">Home</a></li>
                    <li><a href="/about">About</a></li>
                    <li><a href="/contact">Contact</a></li>
                </ul>
            </nav>
        </header>
        
        <div class="container">
            <aside class="sidebar">
                <div class="widget">
                    <h3>Recent Posts</h3>
                    <ul>
                        <li><a href="/post1">Post 1</a></li>
                        <li><a href="/post2">Post 2</a></li>
                    </ul>
                </div>
                <div class="advertisement">
                    <p>Buy our product now!</p>
                </div>
            </aside>
            
            <main class="main-content">
                <article class="post">
                    <header>
                        <h1>Understanding Web Scraping</h1>
                        <p class="meta">Published on January 1, 2024</p>
                    </header>
                    
                    <div class="content">
                        <p>Web scraping is the process of extracting data from websites. 
                        It involves making HTTP requests to web pages and parsing the HTML content.</p>
                        
                        <h2>Types of Web Scraping</h2>
                        <p>There are several approaches to web scraping:</p>
                        <ul>
                            <li>Static scraping for simple HTML pages</li>
                            <li>Dynamic scraping for JavaScript-heavy sites</li>
                            <li>API-based data extraction</li>
                        </ul>
                        
                        <h2>Best Practices</h2>
                        <p>When scraping websites, it's important to:</p>
                        <ol>
                            <li>Respect robots.txt files</li>
                            <li>Implement rate limiting</li>
                            <li>Handle errors gracefully</li>
                            <li>Cache results when appropriate</li>
                        </ol>
                        
                        <blockquote>
                            "The key to successful web scraping is understanding the structure 
                            and behavior of the target website."
                        </blockquote>
                    </div>
                </article>
            </main>
        </div>
        
        <footer class="site-footer">
            <p>&copy; 2024 Example Site. All rights reserved.</p>
        </footer>
    </body>
    </html>
    """
    
    test_file = Path("complex_page.html")
    test_file.write_text(complex_html)
    
    try:
        # Analyze layout
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(complex_html, 'html.parser')
        
        analyzer = LayoutAnalyzer()
        layout_analysis = analyzer.analyze_layout(soup)
        
        print("Layout Analysis:")
        for key, value in layout_analysis.items():
            if key == 'content_blocks':
                print(f"- {key}: {len(value)} blocks found")
                for block in value[:3]:  # Show first 3 blocks
                    print(f"  * {block['type']}: {block['tag']} ({block['text_length']} chars)")
            else:
                print(f"- {key}: {value}")
        
        # Extract content with different strategies
        extractor = SmartContentExtractor()
        extracted = extractor.extract(complex_html)
        
        print(f"\nContent Extraction:")
        print(f"- Title: {extracted.title}")
        print(f"- Description: {extracted.description}")
        print(f"- Language: {extracted.language}")
        print(f"- Word count: {extracted.metadata.get('word_count', 0)}")
        print(f"- Links found: {len(extracted.links)}")
        print(f"- Images found: {len(extracted.images)}")
        
        # Show extracted links
        if extracted.links:
            print(f"\nExtracted Links:")
            for link in extracted.links[:5]:  # Show first 5 links
                print(f"- {link['text']}: {link['url']}")
        
        print(f"\nMain content preview:")
        print(extracted.text[:300] + "...")
        
    finally:
        if test_file.exists():
            test_file.unlink()


def example_4_custom_selectors():
    """Example 4: Using custom content selectors"""
    print("\n=== Example 4: Custom Content Selectors ===")
    
    # HTML with custom structure
    custom_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Custom Structure</title>
    </head>
    <body>
        <div class="wrapper">
            <div class="content-area">
                <div class="primary-content">
                    <h1>Main Article</h1>
                    <p>This is the primary content that should be extracted.</p>
                    <p>It contains the most important information on the page.</p>
                </div>
                <div class="secondary-content">
                    <h2>Related Information</h2>
                    <p>This is secondary content that might also be useful.</p>
                </div>
            </div>
            <div class="noise">
                <p>This is noise content that should be ignored.</p>
            </div>
        </div>
    </body>
    </html>
    """
    
    test_file = Path("custom_structure.html")
    test_file.write_text(custom_html)
    
    try:
        # Configure custom selectors
        extractor_config = ExtractorConfig(
            content_selectors=[
                '.primary-content',      # Try primary content first
                '.content-area',         # Fallback to content area
                '.wrapper'               # Last resort
            ],
            remove_selectors=[
                '.noise',                # Remove noise content
                'script',
                'style'
            ]
        )
        
        extractor = SmartContentExtractor(extractor_config)
        extracted = extractor.extract(custom_html)
        
        print("Custom Selector Results:")
        print(f"- Extracted text length: {len(extracted.text)}")
        print(f"- Content preview: {extracted.text[:200]}...")
        
        # Compare with default extraction
        default_extractor = SmartContentExtractor()
        default_extracted = default_extractor.extract(custom_html)
        
        print(f"\nComparison with default extraction:")
        print(f"- Custom: {len(extracted.text)} chars")
        print(f"- Default: {len(default_extracted.text)} chars")
        
    finally:
        if test_file.exists():
            test_file.unlink()


def example_5_caching_and_performance():
    """Example 5: Caching and performance optimization"""
    print("\n=== Example 5: Caching and Performance ===")
    
    # Configure caching
    loader_config = LoaderConfig(
        requests_per_second=5.0,  # Faster processing
        timeout=15,
        retries=2
    )
    
    config = Config(loader=loader_config)
    
    # Test URL
    test_url = "https://httpbin.org/html"
    
    try:
        loader = HTMLLoaderFactory.create_loader("smart", config.loader)
        
        # First load (should fetch from web)
        print("First load (from web):")
        start_time = time.time()
        documents1 = loader.load(test_url)
        first_load_time = time.time() - start_time
        print(f"- Time: {first_load_time:.2f} seconds")
        print(f"- Cached: {documents1[0].metadata.get('cached', False)}")
        
        # Second load (should use cache)
        print("\nSecond load (from cache):")
        start_time = time.time()
        documents2 = loader.load(test_url)
        second_load_time = time.time() - start_time
        print(f"- Time: {second_load_time:.2f} seconds")
        print(f"- Cached: {documents2[0].metadata.get('cached', False)}")
        
        print(f"\nSpeedup: {first_load_time / second_load_time:.1f}x faster")
        
    except Exception as e:
        print(f"Caching test failed: {e}")


def example_6_pipeline_customization():
    """Example 6: Pipeline customization and optimization"""
    print("\n=== Example 6: Pipeline Customization ===")
    
    # Create custom pipeline configuration
    from langchain_html_processor.core.config import PipelineConfig
    
    pipeline_config = PipelineConfig(
        chunk_size=800,           # Larger chunks
        chunk_overlap=100,        # More overlap
        separators=["\n\n", "\n", ". ", "! ", "? "],  # Custom separators
        parallel_processing=True,
        max_workers=2,
        batch_size=5
    )
    
    config = Config(pipeline=pipeline_config)
    
    # Create test content with multiple sections
    sections = []
    for i in range(5):
        section = f"""
        <section>
            <h2>Section {i+1}</h2>
            <p>This is section {i+1} with substantial content. It contains multiple sentences 
            to demonstrate the chunking behavior. The content is designed to be split across 
            multiple chunks based on the configured chunk size and overlap settings.</p>
            <p>Additional paragraph in section {i+1} with more content to ensure we have 
            enough text for meaningful chunking. This helps test the pipeline's ability 
            to handle longer documents effectively.</p>
        </section>
        """
        sections.append(section)
    
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head><title>Multi-Section Document</title></head>
    <body>
        <main>
            <h1>Multi-Section Document for Pipeline Testing</h1>
            {''.join(sections)}
        </main>
    </body>
    </html>
    """
    
    test_file = Path("multi_section.html")
    test_file.write_text(html_content)
    
    try:
        pipeline = HTMLProcessingPipeline(config)
        result = pipeline.process(test_file, create_vector_store=False)
        
        print("Pipeline Customization Results:")
        print(f"- Original documents: {len(result.documents)}")
        print(f"- Generated chunks: {len(result.chunks)}")
        print(f"- Average chunk size: {result.metadata.get('average_chunk_size', 0):.0f} chars")
        print(f"- Total words: {result.metadata.get('total_words', 0)}")
        
        # Show chunk distribution
        chunk_sizes = [len(chunk.page_content) for chunk in result.chunks]
        print(f"\nChunk size distribution:")
        print(f"- Min: {min(chunk_sizes)} chars")
        print(f"- Max: {max(chunk_sizes)} chars")
        print(f"- Average: {sum(chunk_sizes) / len(chunk_sizes):.0f} chars")
        
        # Show first chunk
        if result.chunks:
            print(f"\nFirst chunk preview:")
            print(result.chunks[0].page_content[:200] + "...")
            
    finally:
        if test_file.exists():
            test_file.unlink()


if __name__ == "__main__":
    # Run examples
    example_1_dynamic_content()
    
    # Run async example
    asyncio.run(example_2_async_batch_processing())
    
    example_3_content_analysis()
    example_4_custom_selectors()
    example_5_caching_and_performance()
    example_6_pipeline_customization()
    
    print("\n=== All Advanced Examples Completed ===")
