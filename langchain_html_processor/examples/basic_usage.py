"""
Basic usage examples for LangChain HTML Processor
"""

import os
from pathlib import Path
from langchain_html_processor.core import (
    HTMLLoaderFactory,
    HTMLProcessingPipeline,
    Config,
    LoaderConfig,
    ExtractorConfig,
    PipelineConfig
)


def example_1_basic_file_loading():
    """Example 1: Load HTML from local file"""
    print("=== Example 1: Basic File Loading ===")
    
    # Create a simple HTML file for testing
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Test Page</title>
        <meta name="description" content="This is a test page for HTML processing">
    </head>
    <body>
        <header>
            <nav>Navigation menu</nav>
        </header>
        <main>
            <article>
                <h1>Main Article Title</h1>
                <p>This is the main content of the article. It contains important information that should be extracted.</p>
                <p>This is another paragraph with more content to demonstrate the extraction capabilities.</p>
            </article>
        </main>
        <footer>Footer content</footer>
    </body>
    </html>
    """
    
    # Save test file
    test_file = Path("test_page.html")
    test_file.write_text(html_content)
    
    try:
        # Load using smart loader
        loader = HTMLLoaderFactory.create_loader("smart")
        documents = loader.load(test_file)
        
        print(f"Loaded {len(documents)} documents")
        for doc in documents:
            print(f"Content length: {len(doc.page_content)}")
            print(f"Metadata: {doc.metadata}")
            print(f"Content preview: {doc.page_content[:200]}...")
            
    finally:
        # Clean up
        if test_file.exists():
            test_file.unlink()


def example_2_url_loading():
    """Example 2: Load HTML from URL"""
    print("\n=== Example 2: URL Loading ===")
    
    # Example URL (using a reliable test site)
    url = "https://httpbin.org/html"
    
    try:
        loader = HTMLLoaderFactory.create_loader("smart")
        documents = loader.load(url)
        
        print(f"Loaded {len(documents)} documents from URL")
        for doc in documents:
            print(f"Source: {doc.metadata.get('source')}")
            print(f"Content length: {len(doc.page_content)}")
            print(f"Content preview: {doc.page_content[:200]}...")
            
    except Exception as e:
        print(f"Failed to load URL: {e}")


def example_3_batch_loading():
    """Example 3: Batch loading multiple URLs"""
    print("\n=== Example 3: Batch Loading ===")
    
    urls = [
        "https://httpbin.org/html",
        "https://httpbin.org/robots.txt"  # This will fail but demonstrate error handling
    ]
    
    try:
        loader = HTMLLoaderFactory.create_loader("batch")
        documents = loader.load(urls)
        
        print(f"Loaded {len(documents)} documents from {len(urls)} URLs")
        for doc in documents:
            print(f"Source: {doc.metadata.get('source')}")
            print(f"Status: {doc.metadata.get('status_code', 'unknown')}")
            print(f"Content length: {len(doc.page_content)}")
            
    except Exception as e:
        print(f"Batch loading failed: {e}")


def example_4_custom_configuration():
    """Example 4: Using custom configuration"""
    print("\n=== Example 4: Custom Configuration ===")
    
    # Create custom configuration
    loader_config = LoaderConfig(
        requests_per_second=1.0,  # Slower rate limiting
        timeout=10,
        retries=2
    )
    
    extractor_config = ExtractorConfig(
        min_text_length=20,  # Lower minimum
        extract_links=True,
        extract_images=True,
        detect_language=True
    )
    
    config = Config(
        loader=loader_config,
        extractor=extractor_config
    )
    
    # Create HTML content with links and images
    html_content = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <title>Test Page with Links</title>
    </head>
    <body>
        <main>
            <h1>Test Article</h1>
            <p>This article contains <a href="https://example.com">external links</a> and images.</p>
            <img src="https://via.placeholder.com/150" alt="Test image">
            <p>More content here with another <a href="/internal">internal link</a>.</p>
        </main>
    </body>
    </html>
    """
    
    test_file = Path("test_links.html")
    test_file.write_text(html_content)
    
    try:
        loader = HTMLLoaderFactory.create_loader("smart", config.loader)
        documents = loader.load(test_file)
        
        print(f"Loaded with custom config:")
        for doc in documents:
            print(f"Content: {doc.page_content[:100]}...")
            print(f"Metadata keys: {list(doc.metadata.keys())}")
            
    finally:
        if test_file.exists():
            test_file.unlink()


def example_5_complete_pipeline():
    """Example 5: Complete processing pipeline"""
    print("\n=== Example 5: Complete Pipeline ===")
    
    # Set up OpenAI API key if available
    if not os.getenv("OPENAI_API_KEY"):
        print("Note: OPENAI_API_KEY not set, skipping vector store creation")
    
    # Create configuration
    config = Config(
        pipeline=PipelineConfig(
            chunk_size=500,
            chunk_overlap=50,
            enable_cache=True
        )
    )
    
    # Create test content
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Long Article for Pipeline Testing</title>
    </head>
    <body>
        <article>
            <h1>The Future of AI</h1>
            <p>Artificial Intelligence is rapidly evolving and transforming various industries. 
            From healthcare to finance, AI applications are becoming more sophisticated and widespread.</p>
            
            <h2>Machine Learning Advances</h2>
            <p>Machine learning algorithms have improved significantly in recent years. 
            Deep learning models can now process complex data patterns with remarkable accuracy.</p>
            
            <h2>Natural Language Processing</h2>
            <p>NLP technologies enable computers to understand and generate human language. 
            This has led to breakthroughs in chatbots, translation services, and content analysis.</p>
            
            <h2>Computer Vision</h2>
            <p>Computer vision systems can now recognize objects, faces, and scenes with high precision. 
            This technology is being used in autonomous vehicles, medical imaging, and security systems.</p>
            
            <h2>Ethical Considerations</h2>
            <p>As AI becomes more powerful, it's crucial to consider ethical implications. 
            Issues like bias, privacy, and job displacement need careful attention.</p>
        </article>
    </body>
    </html>
    """
    
    test_file = Path("long_article.html")
    test_file.write_text(html_content)
    
    try:
        # Process through complete pipeline
        pipeline = HTMLProcessingPipeline(config)
        result = pipeline.process(test_file, create_vector_store=bool(os.getenv("OPENAI_API_KEY")))
        
        print(f"Pipeline Results:")
        print(f"- Documents: {len(result.documents)}")
        print(f"- Chunks: {len(result.chunks)}")
        print(f"- Vector store created: {result.vector_store is not None}")
        print(f"- Total words: {result.metadata.get('total_words', 0)}")
        print(f"- Languages detected: {result.metadata.get('languages_detected', [])}")
        
        # Show chunk examples
        print(f"\nFirst chunk preview:")
        if result.chunks:
            chunk = result.chunks[0]
            print(f"Content: {chunk.page_content[:200]}...")
            print(f"Metadata: {chunk.metadata}")
            
    finally:
        if test_file.exists():
            test_file.unlink()


def example_6_error_handling():
    """Example 6: Error handling and edge cases"""
    print("\n=== Example 6: Error Handling ===")
    
    # Test with invalid URL
    try:
        loader = HTMLLoaderFactory.create_loader("smart")
        documents = loader.load("https://this-url-does-not-exist-12345.com")
        print("This should not print")
    except Exception as e:
        print(f"Expected error for invalid URL: {type(e).__name__}")
    
    # Test with empty HTML
    empty_html = "<html><body></body></html>"
    test_file = Path("empty.html")
    test_file.write_text(empty_html)
    
    try:
        loader = HTMLLoaderFactory.create_loader("smart")
        documents = loader.load(test_file)
        print(f"Empty HTML loaded: {len(documents)} documents")
        if documents:
            print(f"Content length: {len(documents[0].page_content)}")
            
    finally:
        if test_file.exists():
            test_file.unlink()


if __name__ == "__main__":
    # Run all examples
    example_1_basic_file_loading()
    example_2_url_loading()
    example_3_batch_loading()
    example_4_custom_configuration()
    example_5_complete_pipeline()
    example_6_error_handling()
    
    print("\n=== All Examples Completed ===")
