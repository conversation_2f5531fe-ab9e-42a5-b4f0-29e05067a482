#!/usr/bin/env python3
"""
Test runner for LangChain HTML Processor

This script runs basic functionality tests to ensure the system is working correctly.
"""

import sys
import os
import tempfile
from pathlib import Path

# Add the package to Python path
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """Test that all modules can be imported"""
    print("Testing imports...")
    
    try:
        from core import (
            HTMLLoaderFactory,
            HTMLProcessingPipeline,
            Config,
            LoaderConfig,
            ExtractorConfig,
            SmartContentExtractor
        )
        print("✅ All core modules imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False


def test_basic_functionality():
    """Test basic functionality"""
    print("\nTesting basic functionality...")
    
    try:
        from core import HTMLLoaderFactory, SmartContentExtractor
        
        # Test HTML content
        test_html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Test Page</title>
        </head>
        <body>
            <main>
                <h1>Test Title</h1>
                <p>This is test content for the HTML processor.</p>
            </main>
        </body>
        </html>
        """
        
        # Test loader creation
        loader = HTMLLoaderFactory.create_loader("smart")
        print("✅ Smart loader created successfully")
        
        # Test file loading
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
            f.write(test_html)
            temp_file = f.name
        
        try:
            documents = loader.load(temp_file)
            if documents and len(documents) > 0:
                print("✅ File loading works")
                print(f"   Content length: {len(documents[0].page_content)} characters")
            else:
                print("❌ File loading failed - no documents returned")
                return False
        finally:
            os.unlink(temp_file)
        
        # Test content extraction
        extractor = SmartContentExtractor()
        extracted = extractor.extract(test_html)
        
        if extracted and extracted.text:
            print("✅ Content extraction works")
            print(f"   Title: {extracted.title}")
            print(f"   Content preview: {extracted.text[:50]}...")
        else:
            print("❌ Content extraction failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False


def test_configuration():
    """Test configuration system"""
    print("\nTesting configuration...")
    
    try:
        from core import Config, LoaderConfig, ExtractorConfig, PipelineConfig
        
        # Test default configuration
        config = Config()
        print("✅ Default configuration created")
        
        # Test custom configuration
        custom_config = Config(
            loader=LoaderConfig(
                requests_per_second=1.0,
                timeout=10
            ),
            extractor=ExtractorConfig(
                min_text_length=50,
                extract_links=True
            ),
            pipeline=PipelineConfig(
                chunk_size=500,
                chunk_overlap=50
            )
        )
        print("✅ Custom configuration created")
        
        # Test configuration serialization
        config_dict = custom_config.to_dict()
        restored_config = Config.from_dict(config_dict)
        print("✅ Configuration serialization works")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


def test_pipeline():
    """Test processing pipeline"""
    print("\nTesting processing pipeline...")
    
    try:
        from core import HTMLProcessingPipeline, Config, PipelineConfig
        
        # Create test HTML file
        test_html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Pipeline Test</title>
        </head>
        <body>
            <article>
                <h1>Pipeline Test Article</h1>
                <p>This is a longer piece of content designed to test the processing pipeline. 
                It contains multiple sentences and should be split into appropriate chunks.</p>
                <p>This second paragraph adds more content to ensure we have enough text 
                for meaningful chunking and processing through the complete pipeline.</p>
                <p>A third paragraph ensures we have substantial content for testing 
                the text splitting and processing capabilities of the system.</p>
            </article>
        </body>
        </html>
        """
        
        # Configure pipeline without embeddings (to avoid API key requirement)
        config = Config(
            pipeline=PipelineConfig(
                chunk_size=200,
                chunk_overlap=20
            )
        )
        
        pipeline = HTMLProcessingPipeline(config)
        print("✅ Pipeline created successfully")
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
            f.write(test_html)
            temp_file = f.name
        
        try:
            # Process without vector store (no API key needed)
            result = pipeline.process(temp_file, create_vector_store=False)
            
            if result and result.documents and result.chunks:
                print("✅ Pipeline processing works")
                print(f"   Documents: {len(result.documents)}")
                print(f"   Chunks: {len(result.chunks)}")
                print(f"   Total words: {result.metadata.get('total_words', 0)}")
            else:
                print("❌ Pipeline processing failed")
                return False
                
        finally:
            os.unlink(temp_file)
        
        return True
        
    except Exception as e:
        print(f"❌ Pipeline test failed: {e}")
        return False


def test_error_handling():
    """Test error handling"""
    print("\nTesting error handling...")
    
    try:
        from core import HTMLLoaderFactory
        
        loader = HTMLLoaderFactory.create_loader("smart")
        
        # Test invalid file
        try:
            loader.load("nonexistent_file.html")
            print("❌ Should have raised an error for nonexistent file")
            return False
        except Exception:
            print("✅ Properly handles nonexistent file error")
        
        # Test invalid loader type
        try:
            HTMLLoaderFactory.create_loader("invalid_type")
            print("❌ Should have raised an error for invalid loader type")
            return False
        except ValueError:
            print("✅ Properly handles invalid loader type error")
        
        return True
        
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False


def main():
    """Run all tests"""
    print("LangChain HTML Processor - Test Runner")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_basic_functionality,
        test_configuration,
        test_pipeline,
        test_error_handling
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            print(f"\n❌ Test failed: {test.__name__}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The system is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the output above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
