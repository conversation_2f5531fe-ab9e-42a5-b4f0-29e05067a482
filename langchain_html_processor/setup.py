"""
Setup script for Lang<PERSON>hain HTML Processor
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read README file
readme_file = Path(__file__).parent / "README.md"
long_description = readme_file.read_text(encoding="utf-8") if readme_file.exists() else ""

# Read requirements
requirements_file = Path(__file__).parent / "requirements.txt"
if requirements_file.exists():
    with open(requirements_file, 'r', encoding='utf-8') as f:
        requirements = [line.strip() for line in f if line.strip() and not line.startswith('#')]
else:
    requirements = [
        "langchain>=0.1.0",
        "langchain-community>=0.0.20",
        "beautifulsoup4>=4.12.0",
        "lxml>=4.9.0",
        "requests>=2.31.0",
        "aiohttp>=3.8.0",
        "chardet>=5.2.0",
        "pydantic>=2.0.0"
    ]

# Optional dependencies
extras_require = {
    "full": [
        "playwright>=1.40.0",
        "unstructured>=0.10.0",
        "openai>=1.0.0",
        "chromadb>=0.4.0",
        "langdetect>=1.0.9"
    ],
    "dynamic": [
        "playwright>=1.40.0"
    ],
    "structured": [
        "unstructured>=0.10.0"
    ],
    "embeddings": [
        "openai>=1.0.0",
        "chromadb>=0.4.0"
    ],
    "dev": [
        "pytest>=7.4.0",
        "pytest-asyncio>=0.21.0",
        "black>=23.0.0",
        "flake8>=6.0.0",
        "mypy>=1.5.0"
    ]
}

setup(
    name="langchain-html-processor",
    version="1.0.0",
    author="LangChain HTML Processor Team",
    author_email="<EMAIL>",
    description="Comprehensive HTML processing system for LangChain",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-repo/langchain-html-processor",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Text Processing :: Markup :: HTML",
        "Topic :: Internet :: WWW/HTTP :: Indexing/Search",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require=extras_require,
    include_package_data=True,
    package_data={
        "langchain_html_processor": [
            "examples/*.py",
            "tests/*.py",
            "*.md"
        ]
    },
    entry_points={
        "console_scripts": [
            "html-processor-demo=langchain_html_processor.demo:main",
        ],
    },
    keywords=[
        "langchain", "html", "processing", "web-scraping", 
        "content-extraction", "nlp", "ai", "machine-learning"
    ],
    project_urls={
        "Bug Reports": "https://github.com/your-repo/langchain-html-processor/issues",
        "Source": "https://github.com/your-repo/langchain-html-processor",
        "Documentation": "https://docs.example.com",
    },
)
