# LangChain HTML Processor

A comprehensive HTML processing system based on <PERSON><PERSON><PERSON><PERSON> with support for multiple loading strategies, intelligent content extraction, and advanced processing pipelines.

## 🚀 Features

### Multiple HTML Loading Strategies
- **Smart Loader**: Basic BeautifulSoup-based loading with intelligent content extraction
- **Structured Loader**: Structure-preserving loading using Unstructured API
- **Dynamic Loader**: JavaScript rendering with <PERSON>wright for SPA and dynamic content
- **Batch Loader**: High-performance async processing for multiple URLs

### Intelligent Content Extraction
- **Smart Content Detection**: Automatically identifies main content areas
- **Layout Analysis**: Analyzes HTML structure for better extraction
- **Metadata Extraction**: Extracts titles, descriptions, links, and images
- **Language Detection**: Automatic language identification
- **Content Validation**: Ensures quality and relevance of extracted content

### Advanced Processing Pipeline
- **Text Chunking**: Configurable text splitting with overlap
- **Vector Embeddings**: Integration with OpenAI embeddings
- **Vector Storage**: Chroma vector database support
- **Caching**: SQLite-based caching for improved performance
- **Error Handling**: Robust error handling with retry mechanisms

### Production-Ready Features
- **Rate Limiting**: Configurable request throttling
- **Proxy Support**: Proxy rotation for large-scale scraping
- **Encoding Detection**: Automatic character encoding detection
- **Content Filtering**: Configurable selectors for content inclusion/exclusion
- **Parallel Processing**: Multi-threaded processing for better performance

## 📦 Installation

### Prerequisites
```bash
# Install Python 3.8+
python --version

# Install required system dependencies
# For Ubuntu/Debian:
sudo apt-get update
sudo apt-get install -y python3-dev libxml2-dev libxslt1-dev

# For macOS:
brew install libxml2 libxslt
```

### Install Dependencies
```bash
# Clone or download the project
cd langchain_html_processor

# Install Python dependencies
pip install -r requirements.txt

# Optional: Install Playwright for dynamic content
playwright install chromium
```

### Environment Setup
```bash
# Optional: Set OpenAI API key for embeddings
export OPENAI_API_KEY="your-api-key-here"

# Optional: Configure other settings
export HTML_PROCESSOR_CACHE_PATH="./cache"
export HTML_PROCESSOR_LOG_LEVEL="INFO"
```

## 🎯 Quick Start

### Basic Usage

```python
from langchain_html_processor.core import HTMLLoaderFactory, HTMLProcessingPipeline

# Load HTML from file
loader = HTMLLoaderFactory.create_loader("smart")
documents = loader.load("example.html")

# Load HTML from URL
documents = loader.load("https://example.com")

# Process through complete pipeline
pipeline = HTMLProcessingPipeline()
result = pipeline.process("https://example.com")

print(f"Processed {len(result.documents)} documents into {len(result.chunks)} chunks")
```

### Batch Processing

```python
from langchain_html_processor.core import HTMLLoaderFactory

# Process multiple URLs
urls = [
    "https://example.com/page1",
    "https://example.com/page2",
    "https://example.com/page3"
]

loader = HTMLLoaderFactory.create_loader("batch")
documents = loader.load(urls)

print(f"Processed {len(documents)} documents from {len(urls)} URLs")
```

### Dynamic Content

```python
from langchain_html_processor.core import HTMLLoaderFactory

# Load JavaScript-rendered content
loader = HTMLLoaderFactory.create_loader("dynamic")
documents = loader.load("https://spa-example.com")

print(f"Loaded dynamic content: {len(documents[0].page_content)} characters")
```

### Custom Configuration

```python
from langchain_html_processor.core import (
    Config, LoaderConfig, ExtractorConfig, PipelineConfig,
    HTMLProcessingPipeline
)

# Create custom configuration
config = Config(
    loader=LoaderConfig(
        requests_per_second=2.0,
        timeout=30,
        retries=3
    ),
    extractor=ExtractorConfig(
        content_selectors=['.main-content', 'article', 'main'],
        extract_links=True,
        extract_images=True
    ),
    pipeline=PipelineConfig(
        chunk_size=1000,
        chunk_overlap=100
    )
)

# Use custom configuration
pipeline = HTMLProcessingPipeline(config)
result = pipeline.process("https://example.com")
```

## 📚 Documentation

### Core Components

#### HTML Loaders

```python
from langchain_html_processor.core import HTMLLoaderFactory

# Available loader types
loader_types = ["smart", "structured", "dynamic", "batch"]

# Create specific loader
loader = HTMLLoaderFactory.create_loader("smart")

# Auto-select best loader
loader = HTMLLoaderFactory.get_best_loader(source)
```

#### Content Extractors

```python
from langchain_html_processor.core import SmartContentExtractor, ExtractorConfig

# Configure extractor
config = ExtractorConfig(
    content_selectors=['article', '.content', 'main'],
    remove_selectors=['nav', 'footer', '.ads'],
    extract_links=True,
    detect_language=True
)

extractor = SmartContentExtractor(config)
extracted = extractor.extract(html_content, url)

print(f"Title: {extracted.title}")
print(f"Language: {extracted.language}")
print(f"Links: {len(extracted.links)}")
```

#### Processing Pipeline

```python
from langchain_html_processor.core import HTMLProcessingPipeline

pipeline = HTMLProcessingPipeline()

# Process single source
result = pipeline.process("https://example.com")

# Process multiple sources
results = pipeline.process_batch([
    "https://example.com/page1",
    "https://example.com/page2"
])

# Access results
for result in results:
    print(f"Documents: {len(result.documents)}")
    print(f"Chunks: {len(result.chunks)}")
    print(f"Vector store: {result.vector_store is not None}")
```

### Configuration Options

#### Loader Configuration
```python
from langchain_html_processor.core import LoaderConfig

config = LoaderConfig(
    encoding="utf-8",                    # Text encoding
    timeout=30,                          # Request timeout
    retries=3,                           # Retry attempts
    requests_per_second=2.0,             # Rate limiting
    user_agent="Custom User Agent",      # User agent string
    proxies=["http://proxy1:8080"],      # Proxy list
    enable_js=True,                      # JavaScript rendering
    min_content_length=100               # Minimum content length
)
```

#### Extractor Configuration
```python
from langchain_html_processor.core import ExtractorConfig

config = ExtractorConfig(
    content_selectors=[                  # Content area selectors
        'article', '.post-content', 'main'
    ],
    remove_selectors=[                   # Elements to remove
        'script', 'style', '.ads'
    ],
    min_text_length=50,                  # Minimum text length
    extract_links=True,                  # Extract links
    extract_images=True,                 # Extract images
    detect_language=True,                # Language detection
    supported_languages=['en', 'zh']    # Supported languages
)
```

#### Pipeline Configuration
```python
from langchain_html_processor.core import PipelineConfig

config = PipelineConfig(
    chunk_size=1500,                     # Chunk size
    chunk_overlap=200,                   # Chunk overlap
    separators=["\n\n", "\n", "."],      # Text separators
    embedding_model="text-embedding-ada-002",  # Embedding model
    vector_store_type="chroma",          # Vector store type
    enable_cache=True,                   # Enable caching
    cache_ttl=3600,                      # Cache TTL (seconds)
    parallel_processing=True,            # Parallel processing
    max_workers=4                        # Max worker threads
)
```

## 🔧 Advanced Usage

### Error Handling and Retries

```python
from langchain_html_processor.core import HTMLLoaderFactory, LoaderConfig

# Configure robust error handling
config = LoaderConfig(
    retries=5,                           # More retry attempts
    timeout=60,                          # Longer timeout
    requests_per_second=1.0              # Conservative rate limiting
)

loader = HTMLLoaderFactory.create_loader("smart", config)

try:
    documents = loader.load("https://difficult-site.com")
    print(f"Successfully loaded {len(documents)} documents")
except Exception as e:
    print(f"Failed after all retries: {e}")
```

### Proxy Rotation

```python
from langchain_html_processor.core import LoaderConfig, HTMLLoaderFactory

# Configure proxy rotation
config = LoaderConfig(
    proxies=[
        "http://proxy1:8080",
        "http://proxy2:8080",
        "http://proxy3:8080"
    ],
    rotate_proxy=True
)

loader = HTMLLoaderFactory.create_loader("batch", config)
documents = loader.load(urls)
```

### Custom Content Selectors

```python
from langchain_html_processor.core import SmartContentExtractor, ExtractorConfig

# Configure for specific site structure
config = ExtractorConfig(
    content_selectors=[
        '.article-body',                 # Primary content
        '.post-content',                 # Secondary content
        '#main-content'                  # Fallback content
    ],
    remove_selectors=[
        '.social-share',                 # Remove social buttons
        '.related-posts',                # Remove related posts
        '.comment-section'               # Remove comments
    ]
)

extractor = SmartContentExtractor(config)
```

### Caching and Performance

```python
from langchain_html_processor.core import Config, PipelineConfig

# Optimize for performance
config = Config(
    pipeline=PipelineConfig(
        enable_cache=True,
        cache_ttl=7200,                  # 2 hour cache
        parallel_processing=True,
        max_workers=8,                   # More workers
        batch_size=20                    # Larger batches
    )
)

pipeline = HTMLProcessingPipeline(config)
```

## 🎮 Interactive Demo

Run the interactive demo to explore all features:

```bash
cd langchain_html_processor
python demo.py
```

The demo includes:
1. Basic file loading
2. URL loading with caching
3. Batch processing
4. Dynamic content handling
5. Content analysis
6. Complete pipeline processing
7. Configuration examples
8. Error handling demonstrations

## 📋 Examples

### Example 1: News Article Processing

```python
from langchain_html_processor.core import HTMLProcessingPipeline, Config, ExtractorConfig

# Configure for news articles
config = Config(
    extractor=ExtractorConfig(
        content_selectors=[
            'article', '.article-body', '.story-content'
        ],
        remove_selectors=[
            '.advertisement', '.social-share', '.related-articles'
        ],
        extract_links=True,
        detect_language=True
    )
)

pipeline = HTMLProcessingPipeline(config)

# Process news articles
news_urls = [
    "https://news-site.com/article1",
    "https://news-site.com/article2"
]

results = pipeline.process_batch(news_urls)

for result in results:
    for doc in result.documents:
        print(f"Title: {doc.metadata.get('title')}")
        print(f"Language: {doc.metadata.get('language')}")
        print(f"Word count: {doc.metadata.get('word_count')}")
```

### Example 2: E-commerce Product Pages

```python
from langchain_html_processor.core import SmartContentExtractor, ExtractorConfig

# Configure for product pages
config = ExtractorConfig(
    content_selectors=[
        '.product-description', '.product-details', '.product-info'
    ],
    remove_selectors=[
        '.reviews', '.recommendations', '.ads'
    ],
    extract_images=True,  # Important for products
    extract_links=True
)

extractor = SmartContentExtractor(config)

# Process product page
with open('product_page.html', 'r') as f:
    html_content = f.read()

extracted = extractor.extract(html_content, "https://shop.com/product")

print(f"Product title: {extracted.title}")
print(f"Description: {extracted.text[:200]}...")
print(f"Images found: {len(extracted.images)}")
```

### Example 3: Documentation Processing

```python
from langchain_html_processor.core import HTMLProcessingPipeline, Config, PipelineConfig

# Configure for documentation
config = Config(
    pipeline=PipelineConfig(
        chunk_size=800,                  # Smaller chunks for docs
        chunk_overlap=80,
        separators=["\n\n", "\n", ".", "!", "?"]
    )
)

pipeline = HTMLProcessingPipeline(config)

# Process documentation
doc_urls = [
    "https://docs.example.com/guide1",
    "https://docs.example.com/guide2"
]

result = pipeline.process(doc_urls)

# Create retriever for Q&A
if result.vector_store:
    retriever = pipeline.create_retriever(
        result.vector_store,
        search_kwargs={"k": 3}
    )

    # Use with LangChain QA chain
    from langchain.chains import RetrievalQA
    from langchain.llms import OpenAI

    qa_chain = RetrievalQA.from_chain_type(
        llm=OpenAI(),
        retriever=retriever
    )

    answer = qa_chain.run("How do I configure the system?")
    print(f"Answer: {answer}")
```

## 🧪 Testing

### Run Basic Tests

```bash
# Run examples to test functionality
cd langchain_html_processor

# Test basic usage
python examples/basic_usage.py

# Test advanced features
python examples/advanced_usage.py

# Run interactive demo
python demo.py
```

### Unit Testing

```python
# Create test files in tests/ directory
import unittest
from langchain_html_processor.core import HTMLLoaderFactory

class TestHTMLLoader(unittest.TestCase):
    def test_smart_loader(self):
        loader = HTMLLoaderFactory.create_loader("smart")
        self.assertIsNotNone(loader)

    def test_file_loading(self):
        # Create test HTML file
        test_html = "<html><body><p>Test content</p></body></html>"
        with open("test.html", "w") as f:
            f.write(test_html)

        loader = HTMLLoaderFactory.create_loader("smart")
        docs = loader.load("test.html")

        self.assertEqual(len(docs), 1)
        self.assertIn("Test content", docs[0].page_content)

        # Cleanup
        import os
        os.remove("test.html")

if __name__ == "__main__":
    unittest.main()
```

## 🚨 Common Issues and Solutions

### Issue 1: Playwright Installation
```bash
# Error: Playwright not found
# Solution: Install Playwright browsers
playwright install chromium

# Or install all browsers
playwright install
```

### Issue 2: Encoding Problems
```python
# Error: UnicodeDecodeError
# Solution: Use encoding detection
from langchain_html_processor.core import LoaderConfig

config = LoaderConfig(encoding="auto")  # Auto-detect encoding
loader = HTMLLoaderFactory.create_loader("smart", config)
```

### Issue 3: Rate Limiting
```python
# Error: Too many requests
# Solution: Reduce request rate
config = LoaderConfig(
    requests_per_second=0.5,  # Slower rate
    delay_between_requests=2.0  # Additional delay
)
```

### Issue 4: Memory Issues with Large Files
```python
# Error: Memory overflow
# Solution: Use streaming and smaller chunks
config = Config(
    pipeline=PipelineConfig(
        chunk_size=500,        # Smaller chunks
        batch_size=5,          # Smaller batches
        max_workers=2          # Fewer workers
    )
)
```

### Issue 5: JavaScript Content Not Loading
```python
# Error: Empty content from SPA
# Solution: Use dynamic loader with longer wait time
config = LoaderConfig(
    enable_js=True,
    wait_for_js=5.0,          # Wait longer for JS
    timeout=60                # Longer timeout
)

loader = HTMLLoaderFactory.create_loader("dynamic", config)
```

## 🔍 Best Practices

### 1. Choose the Right Loader
- **Static HTML**: Use `smart` loader
- **JavaScript-heavy sites**: Use `dynamic` loader
- **Multiple URLs**: Use `batch` loader
- **Structured content**: Use `structured` loader

### 2. Configure Rate Limiting
```python
# Be respectful to websites
config = LoaderConfig(
    requests_per_second=1.0,   # Conservative rate
    timeout=30,                # Reasonable timeout
    retries=3                  # Limited retries
)
```

### 3. Optimize Content Extraction
```python
# Use specific selectors for better results
config = ExtractorConfig(
    content_selectors=[
        'article',             # Try article first
        '.main-content',       # Then main content
        'main',                # Then main tag
        'body'                 # Fallback to body
    ]
)
```

### 4. Handle Errors Gracefully
```python
try:
    result = pipeline.process(url)
    if result.documents:
        print("Success!")
    else:
        print("No content extracted")
except Exception as e:
    print(f"Processing failed: {e}")
```

### 5. Use Caching for Development
```python
# Enable caching during development
config = Config(
    pipeline=PipelineConfig(
        enable_cache=True,
        cache_ttl=3600  # 1 hour cache
    )
)
```

## 📊 Performance Tips

### 1. Batch Processing
```python
# Process multiple URLs together
urls = ["url1", "url2", "url3"]
loader = HTMLLoaderFactory.create_loader("batch")
documents = loader.load(urls)  # Faster than individual loads
```

### 2. Parallel Processing
```python
# Enable parallel processing
config = Config(
    pipeline=PipelineConfig(
        parallel_processing=True,
        max_workers=4
    )
)
```

### 3. Optimize Chunk Size
```python
# Balance chunk size for your use case
config = PipelineConfig(
    chunk_size=1000,      # Larger for better context
    chunk_overlap=100     # Smaller for less redundancy
)
```

### 4. Use Appropriate Selectors
```python
# Specific selectors are faster
config = ExtractorConfig(
    content_selectors=['.article-content'],  # Specific
    # vs
    content_selectors=['div', 'p', 'span']   # Too broad
)
```

## 🤝 Contributing

We welcome contributions! Please see our contributing guidelines:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

### Development Setup
```bash
# Clone the repository
git clone https://github.com/your-repo/langchain-html-processor.git
cd langchain-html-processor

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install development dependencies
pip install -r requirements.txt
pip install -e .

# Install pre-commit hooks
pre-commit install
```

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- [LangChain](https://github.com/hwchase17/langchain) for the excellent framework
- [BeautifulSoup](https://www.crummy.com/software/BeautifulSoup/) for HTML parsing
- [Playwright](https://playwright.dev/) for dynamic content handling
- [Unstructured](https://github.com/Unstructured-IO/unstructured) for structured processing

## 📞 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/example)
- 📖 Documentation: [Full documentation](https://docs.example.com)
- 🐛 Issues: [GitHub Issues](https://github.com/your-repo/issues)

---

**Built with ❤️ for the AI and web scraping community**
```
```
