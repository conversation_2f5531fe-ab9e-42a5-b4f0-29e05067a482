#!/usr/bin/env python3
"""
LangChain HTML Processor Demo

Interactive demonstration of HTML processing capabilities
"""

import os
import sys
import time
from pathlib import Path
from typing import Optional

# Add the package to Python path
sys.path.insert(0, str(Path(__file__).parent))

from core import (
    HTMLLoaderFactory,
    HTMLProcessingPipeline,
    Config,
    LoaderConfig,
    ExtractorConfig,
    PipelineConfig
)


def print_banner():
    """Print demo banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                LangChain HTML Processor Demo                 ║
    ║                                                              ║
    ║  A comprehensive HTML processing system based on LangChain   ║
    ║  with support for multiple loading strategies and advanced   ║
    ║  content extraction capabilities.                            ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def demo_menu():
    """Display demo menu"""
    menu = """
    Available Demos:
    
    1. Basic File Loading - Load and process local HTML files
    2. URL Loading - Fetch and process web pages
    3. Batch Processing - Process multiple URLs efficiently
    4. Dynamic Content - Handle JavaScript-rendered pages
    5. Content Analysis - Advanced content extraction and analysis
    6. Complete Pipeline - Full processing with chunking and embeddings
    7. Configuration Demo - Custom settings and optimization
    8. Error Handling - Demonstrate robust error handling
    
    0. Exit
    
    """
    print(menu)


def create_sample_html() -> Path:
    """Create a sample HTML file for testing"""
    html_content = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <title>LangChain HTML Processor Demo Page</title>
        <meta name="description" content="A demo page showcasing HTML processing capabilities">
        <meta name="keywords" content="HTML, processing, LangChain, demo">
    </head>
    <body>
        <header>
            <nav>
                <ul>
                    <li><a href="/">Home</a></li>
                    <li><a href="/about">About</a></li>
                    <li><a href="/contact">Contact</a></li>
                </ul>
            </nav>
        </header>
        
        <main>
            <article>
                <h1>Welcome to LangChain HTML Processor</h1>
                
                <p>This is a demonstration of the LangChain HTML Processor, a powerful tool 
                for extracting and processing content from HTML documents. The system supports 
                multiple loading strategies and advanced content extraction techniques.</p>
                
                <h2>Key Features</h2>
                <ul>
                    <li><strong>Multiple Loading Strategies:</strong> Support for static files, 
                    dynamic web pages, and batch processing</li>
                    <li><strong>Smart Content Extraction:</strong> Intelligent identification 
                    of main content areas</li>
                    <li><strong>Configurable Processing:</strong> Customizable selectors, 
                    filters, and processing options</li>
                    <li><strong>Error Handling:</strong> Robust error handling with retry 
                    mechanisms and fallback strategies</li>
                </ul>
                
                <h2>Processing Pipeline</h2>
                <p>The processing pipeline includes several stages:</p>
                <ol>
                    <li>HTML Loading - Fetch content from files or URLs</li>
                    <li>Content Extraction - Extract main content and metadata</li>
                    <li>Text Processing - Clean and normalize text content</li>
                    <li>Chunking - Split content into manageable pieces</li>
                    <li>Embedding - Generate vector embeddings (optional)</li>
                    <li>Storage - Store in vector database for retrieval</li>
                </ol>
                
                <blockquote>
                    "The LangChain HTML Processor transforms raw HTML into structured, 
                    searchable knowledge that can be easily integrated into AI applications."
                </blockquote>
                
                <h2>Use Cases</h2>
                <p>This tool is perfect for:</p>
                <ul>
                    <li>Building knowledge bases from web content</li>
                    <li>Creating RAG (Retrieval-Augmented Generation) systems</li>
                    <li>Content analysis and research</li>
                    <li>Web scraping and data extraction</li>
                    <li>Document processing workflows</li>
                </ul>
            </article>
        </main>
        
        <footer>
            <p>&copy; 2024 LangChain HTML Processor Demo. Built with ❤️ for the AI community.</p>
        </footer>
    </body>
    </html>
    """
    
    demo_file = Path("demo_page.html")
    demo_file.write_text(html_content)
    return demo_file


def demo_1_basic_loading():
    """Demo 1: Basic file loading"""
    print("\n" + "="*60)
    print("DEMO 1: Basic File Loading")
    print("="*60)
    
    # Create sample file
    demo_file = create_sample_html()
    
    try:
        print(f"Loading HTML file: {demo_file}")
        
        loader = HTMLLoaderFactory.create_loader("smart")
        documents = loader.load(demo_file)
        
        print(f"\n✅ Successfully loaded {len(documents)} document(s)")
        
        for i, doc in enumerate(documents):
            print(f"\nDocument {i+1}:")
            print(f"  Source: {doc.metadata.get('source')}")
            print(f"  Type: {doc.metadata.get('type')}")
            print(f"  Encoding: {doc.metadata.get('encoding')}")
            print(f"  Content length: {len(doc.page_content)} characters")
            print(f"  Content preview: {doc.page_content[:150]}...")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        if demo_file.exists():
            demo_file.unlink()
    
    input("\nPress Enter to continue...")


def demo_2_url_loading():
    """Demo 2: URL loading"""
    print("\n" + "="*60)
    print("DEMO 2: URL Loading")
    print("="*60)
    
    test_url = "https://httpbin.org/html"
    print(f"Loading content from: {test_url}")
    
    try:
        loader = HTMLLoaderFactory.create_loader("smart")
        
        start_time = time.time()
        documents = loader.load(test_url)
        load_time = time.time() - start_time
        
        print(f"\n✅ Successfully loaded in {load_time:.2f} seconds")
        
        for doc in documents:
            print(f"\nDocument details:")
            print(f"  Source: {doc.metadata.get('source')}")
            print(f"  Status code: {doc.metadata.get('status_code')}")
            print(f"  Content length: {len(doc.page_content)} characters")
            print(f"  Cached: {doc.metadata.get('cached', False)}")
            print(f"  Content preview: {doc.page_content[:150]}...")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    input("\nPress Enter to continue...")


def demo_3_batch_processing():
    """Demo 3: Batch processing"""
    print("\n" + "="*60)
    print("DEMO 3: Batch Processing")
    print("="*60)
    
    urls = [
        "https://httpbin.org/html",
        "https://httpbin.org/json",
        "https://httpbin.org/xml"
    ]
    
    print(f"Processing {len(urls)} URLs in batch:")
    for url in urls:
        print(f"  - {url}")
    
    try:
        loader = HTMLLoaderFactory.create_loader("batch")
        
        start_time = time.time()
        documents = loader.load(urls)
        load_time = time.time() - start_time
        
        print(f"\n✅ Batch processing completed in {load_time:.2f} seconds")
        print(f"Successfully processed {len(documents)} out of {len(urls)} URLs")
        
        for doc in documents:
            source = doc.metadata.get('source', 'unknown')
            status = doc.metadata.get('status_code', 'unknown')
            length = len(doc.page_content)
            print(f"  - {source}: Status {status}, {length} chars")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    input("\nPress Enter to continue...")


def demo_4_dynamic_content():
    """Demo 4: Dynamic content loading"""
    print("\n" + "="*60)
    print("DEMO 4: Dynamic Content Loading")
    print("="*60)
    
    print("Note: This demo requires Playwright to be installed.")
    print("If not installed, it will fall back to static loading.")
    
    dynamic_url = "https://quotes.toscrape.com/js/"
    print(f"\nLoading JavaScript-rendered content from: {dynamic_url}")
    
    try:
        loader = HTMLLoaderFactory.create_loader("dynamic")
        
        start_time = time.time()
        documents = loader.load(dynamic_url)
        load_time = time.time() - start_time
        
        print(f"\n✅ Dynamic loading completed in {load_time:.2f} seconds")
        
        for doc in documents:
            print(f"\nDocument details:")
            print(f"  Source: {doc.metadata.get('source')}")
            print(f"  Title: {doc.metadata.get('title', 'No title')}")
            print(f"  Loader type: {doc.metadata.get('loader_type')}")
            print(f"  Content length: {len(doc.page_content)} characters")
            print(f"  Content preview: {doc.page_content[:150]}...")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        print("This is expected if Playwright is not installed.")
    
    input("\nPress Enter to continue...")


def demo_5_content_analysis():
    """Demo 5: Content analysis"""
    print("\n" + "="*60)
    print("DEMO 5: Content Analysis")
    print("="*60)
    
    demo_file = create_sample_html()
    
    try:
        from core.content_extractor import SmartContentExtractor
        from core.config import ExtractorConfig
        
        # Configure extractor for detailed analysis
        config = ExtractorConfig(
            extract_links=True,
            extract_images=True,
            detect_language=True
        )
        
        extractor = SmartContentExtractor(config)
        
        print(f"Analyzing content from: {demo_file}")
        
        with open(demo_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        extracted = extractor.extract(html_content)
        
        print(f"\n✅ Content analysis completed")
        print(f"\nExtracted Information:")
        print(f"  Title: {extracted.title}")
        print(f"  Description: {extracted.description}")
        print(f"  Language: {extracted.language}")
        print(f"  Word count: {extracted.metadata.get('word_count', 0)}")
        print(f"  Character count: {extracted.metadata.get('char_count', 0)}")
        print(f"  Links found: {len(extracted.links)}")
        print(f"  Images found: {len(extracted.images)}")
        
        if extracted.links:
            print(f"\n  Sample links:")
            for link in extracted.links[:3]:
                print(f"    - {link['text']}: {link['url']}")
        
        print(f"\n  Content preview:")
        print(f"    {extracted.text[:200]}...")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        if demo_file.exists():
            demo_file.unlink()
    
    input("\nPress Enter to continue...")


def demo_6_complete_pipeline():
    """Demo 6: Complete processing pipeline"""
    print("\n" + "="*60)
    print("DEMO 6: Complete Processing Pipeline")
    print("="*60)
    
    demo_file = create_sample_html()
    
    try:
        # Configure pipeline
        config = Config(
            pipeline=PipelineConfig(
                chunk_size=500,
                chunk_overlap=50,
                enable_cache=True
            )
        )
        
        print(f"Running complete pipeline on: {demo_file}")
        print("Note: Vector store creation requires OpenAI API key")
        
        pipeline = HTMLProcessingPipeline(config)
        
        start_time = time.time()
        result = pipeline.process(
            demo_file, 
            create_vector_store=bool(os.getenv("OPENAI_API_KEY"))
        )
        process_time = time.time() - start_time
        
        print(f"\n✅ Pipeline completed in {process_time:.2f} seconds")
        print(f"\nPipeline Results:")
        print(f"  Documents processed: {len(result.documents)}")
        print(f"  Chunks created: {len(result.chunks)}")
        print(f"  Vector store created: {result.vector_store is not None}")
        print(f"  Total words: {result.metadata.get('total_words', 0)}")
        print(f"  Total characters: {result.metadata.get('total_characters', 0)}")
        print(f"  Average chunk size: {result.metadata.get('average_chunk_size', 0):.0f}")
        print(f"  Languages detected: {result.metadata.get('languages_detected', [])}")
        
        if result.chunks:
            print(f"\n  First chunk preview:")
            chunk = result.chunks[0]
            print(f"    Chunk ID: {chunk.metadata.get('chunk_id')}")
            print(f"    Chunk size: {chunk.metadata.get('chunk_size')}")
            print(f"    Content: {chunk.page_content[:150]}...")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        if demo_file.exists():
            demo_file.unlink()
    
    input("\nPress Enter to continue...")


def demo_7_configuration():
    """Demo 7: Configuration options"""
    print("\n" + "="*60)
    print("DEMO 7: Configuration Demo")
    print("="*60)
    
    print("Demonstrating different configuration options...")
    
    # Create test content
    demo_file = create_sample_html()
    
    try:
        # Default configuration
        print("\n1. Default Configuration:")
        default_loader = HTMLLoaderFactory.create_loader("smart")
        default_docs = default_loader.load(demo_file)
        print(f"   Content length: {len(default_docs[0].page_content)} chars")
        
        # Custom configuration
        print("\n2. Custom Configuration:")
        custom_config = LoaderConfig(
            requests_per_second=1.0,
            timeout=5,
            retries=1
        )
        custom_loader = HTMLLoaderFactory.create_loader("smart", custom_config)
        custom_docs = custom_loader.load(demo_file)
        print(f"   Content length: {len(custom_docs[0].page_content)} chars")
        print(f"   Rate limit: {custom_config.requests_per_second} req/sec")
        print(f"   Timeout: {custom_config.timeout} seconds")
        
        # Pipeline configuration
        print("\n3. Pipeline Configuration:")
        pipeline_config = Config(
            pipeline=PipelineConfig(
                chunk_size=300,
                chunk_overlap=30
            )
        )
        pipeline = HTMLProcessingPipeline(pipeline_config)
        result = pipeline.process(demo_file, create_vector_store=False)
        print(f"   Chunks created: {len(result.chunks)}")
        print(f"   Average chunk size: {result.metadata.get('average_chunk_size', 0):.0f}")
        
        print(f"\n✅ Configuration demo completed")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        if demo_file.exists():
            demo_file.unlink()
    
    input("\nPress Enter to continue...")


def demo_8_error_handling():
    """Demo 8: Error handling"""
    print("\n" + "="*60)
    print("DEMO 8: Error Handling Demo")
    print("="*60)
    
    print("Demonstrating robust error handling...")
    
    # Test cases
    test_cases = [
        ("Invalid URL", "https://this-url-definitely-does-not-exist-12345.com"),
        ("Non-existent file", "non_existent_file.html"),
        ("Empty content", ""),
    ]
    
    loader = HTMLLoaderFactory.create_loader("smart")
    
    for test_name, test_input in test_cases:
        print(f"\n{test_name}:")
        try:
            if test_name == "Empty content":
                # Create empty file
                empty_file = Path("empty.html")
                empty_file.write_text("<html><body></body></html>")
                documents = loader.load(empty_file)
                print(f"   ✅ Handled gracefully: {len(documents)} documents")
                if documents:
                    print(f"   Content length: {len(documents[0].page_content)}")
                empty_file.unlink()
            else:
                documents = loader.load(test_input)
                print(f"   ✅ Unexpected success: {len(documents)} documents")
        except Exception as e:
            print(f"   ✅ Error handled gracefully: {type(e).__name__}")
    
    print(f"\n✅ Error handling demo completed")
    input("\nPress Enter to continue...")


def main():
    """Main demo function"""
    print_banner()
    
    demos = {
        "1": demo_1_basic_loading,
        "2": demo_2_url_loading,
        "3": demo_3_batch_processing,
        "4": demo_4_dynamic_content,
        "5": demo_5_content_analysis,
        "6": demo_6_complete_pipeline,
        "7": demo_7_configuration,
        "8": demo_8_error_handling,
    }
    
    while True:
        demo_menu()
        choice = input("Select a demo (0-8): ").strip()
        
        if choice == "0":
            print("\nThank you for trying LangChain HTML Processor!")
            print("Visit our documentation for more information.")
            break
        elif choice in demos:
            demos[choice]()
        else:
            print("Invalid choice. Please select 0-8.")


if __name__ == "__main__":
    main()
