#!/usr/bin/env python3
"""
ETF机器人一键执行脚本 - 优化版本
提供ETF数据获取、分析和AI智能建议的完整流程
"""
import os
import sys
import logging
import datetime
from datetime import date
from typing import List, Dict, Any

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
# 如果从上级目录运行，也添加 etf_optimized 路径
if 'etf_optimized' not in current_dir:
    etf_optimized_path = os.path.join(os.getcwd(), 'etf_optimized')
    if os.path.exists(etf_optimized_path):
        sys.path.append(etf_optimized_path)

from etf_core.data import UnifiedETFDataFetcher, ETFConfig, DataSource
from etf_core.analysis import EnhancedETFAnalyzer, analyze_etfs_enhanced
from etf_core.ai import get_ai_analysis, generate_professional_prompt
from etf_core.strategy import StrategyManager, create_strategy_report
from etf_core.utils import config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('etf_robot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ETFRobot:
    """ETF投资机器人主类"""
    
    def __init__(self):
        """初始化ETF机器人"""
        self.config = ETFConfig()

        # 使用统一数据获取器，支持多数据源
        # 检查Tushare是否可用
        try:
            from etf_core.data.tushare_fetcher import TUSHARE_AVAILABLE
        except ImportError:
            TUSHARE_AVAILABLE = False

        if TUSHARE_AVAILABLE and config.TUSHARE_PRIMARY:
            primary_source = DataSource.TUSHARE
            fallback_sources = [DataSource.AKSHARE]
        else:
            primary_source = DataSource.AKSHARE
            fallback_sources = [DataSource.TUSHARE] if TUSHARE_AVAILABLE else []

        self.data_fetcher = UnifiedETFDataFetcher(
            tushare_token=config.TUSHARE_TOKEN,
            primary_source=primary_source,
            fallback_sources=fallback_sources,
            max_retries=config.MAX_RETRIES,
            retry_delay=config.RETRY_DELAY
        )

        self.analyzer = EnhancedETFAnalyzer()
        self.strategy_manager = StrategyManager()

        logger.info("ETF投资机器人初始化完成（使用统一数据获取器）")
    
    def fetch_current_prices(self, etf_list: List[Dict]) -> List[Dict]:
        """获取ETF当前价格，优化版本"""
        logger.info("开始获取ETF当前价格...")

        # 提取ETF代码列表
        symbols = [etf['symbol'] for etf in etf_list]

        # 批量获取价格
        price_results = self.data_fetcher.get_batch_prices(symbols)

        # 处理结果
        updated_etfs = []
        failed_etfs = []

        for etf in etf_list:
            symbol = etf['symbol']
            current_price = price_results.get(symbol)

            if current_price is not None:
                etf_copy = etf.copy()
                etf_copy['current_price'] = current_price
                updated_etfs.append(etf_copy)
                logger.info(f"✅ {etf['name']}: {current_price:.3f}元")
            else:
                failed_etfs.append(etf['name'])
                logger.warning(f"❌ 无法获取 {etf['name']} ({symbol}) 的价格")

        # 记录统计信息
        success_count = len(updated_etfs)
        total_count = len(etf_list)
        failed_count = len(failed_etfs)

        logger.info(f"价格获取完成: 成功 {success_count}/{total_count}, 失败 {failed_count}")

        if failed_etfs:
            logger.warning(f"获取失败的ETF: {', '.join(failed_etfs)}")
            logger.info("失败原因可能包括: 网络问题、ETF代码错误、数据源不可用、ETF停牌等")

        return updated_etfs
    
    def analyze_portfolio(self, etf_list: List[Dict]) -> Dict[str, Any]:
        """分析投资组合"""
        logger.info("开始分析投资组合...")
        
        # 基础分析
        analysis_results = []
        for etf in etf_list:
            result = self.analyzer.comprehensive_analysis(etf)
            analysis_results.append(result)
        
        # 计算组合摘要
        total_value = sum(etf.get('current_price', 0) * etf.get('position', 0) for etf in etf_list)
        total_cost = sum(etf.get('cost_price', 0) * etf.get('position', 0) for etf in etf_list)
        total_return = ((total_value - total_cost) / total_cost * 100) if total_cost > 0 else 0
        
        # 板块分布
        sector_dist = {}
        for etf in etf_list:
            sector = etf.get('sector', '未知')
            value = etf.get('current_price', 0) * etf.get('position', 0)
            sector_dist[sector] = sector_dist.get(sector, 0) + value
        
        portfolio_summary = {
            '总市值': total_value,
            '总成本': total_cost,
            '总收益率': f"{total_return:.2f}%",
            'ETF数量': len(etf_list),
            '板块分布': sector_dist
        }
        
        logger.info("投资组合分析完成")
        return {
            'analysis_results': analysis_results,
            'portfolio_summary': portfolio_summary
        }
    
    def generate_ai_analysis(self, etf_list: List[Dict], portfolio_summary: Dict) -> str:
        """生成AI分析报告"""
        logger.info("开始生成AI分析报告...")
        
        try:
            # 生成专业提示词
            prompt = generate_professional_prompt(etf_list, portfolio_summary)
            
            # 获取AI分析
            ai_analysis = get_ai_analysis(prompt)
            
            if ai_analysis:
                logger.info("AI分析报告生成成功")
                return ai_analysis
            else:
                logger.warning("AI分析报告生成失败")
                return "AI分析暂时不可用，请稍后重试。"
        
        except Exception as e:
            logger.error(f"AI分析过程中出错: {e}")
            return f"AI分析过程中出错: {e}"
    
    def generate_strategy_report(self, etf_list: List[Dict]) -> Dict[str, Any]:
        """生成策略报告"""
        logger.info("开始生成策略报告...")
        
        try:
            strategy_report = create_strategy_report(etf_list, self.strategy_manager)
            logger.info("策略报告生成完成")
            return strategy_report
        except Exception as e:
            logger.error(f"策略报告生成失败: {e}")
            return {}
    
    def send_notification(self, message: str) -> bool:
        """发送Server酱通知"""
        if not config.SERVER_CHAN_KEY:
            logger.info("未配置Server酱密钥，跳过通知发送")
            return False
        
        try:
            import requests
            url = f"https://sctapi.ftqq.com/{config.SERVER_CHAN_KEY}.send"
            data = {
                "title": "ETF投资机器人报告",
                "desp": message
            }
            response = requests.post(url, data=data, timeout=10)
            
            if response.status_code == 200:
                logger.info("Server酱通知发送成功")
                return True
            else:
                logger.warning(f"Server酱通知发送失败: {response.status_code}")
                return False
        
        except Exception as e:
            logger.error(f"发送通知时出错: {e}")
            return False
    
    def run_analysis(self) -> Dict[str, Any]:
        """运行完整的ETF分析流程"""
        logger.info("=" * 50)
        logger.info("ETF投资机器人开始运行")
        logger.info("=" * 50)
        
        try:
            # 1. 加载ETF配置
            etf_list = self.config.etf_list
            logger.info(f"加载了 {len(etf_list)} 个ETF配置")
            
            # 2. 获取当前价格
            etf_list_with_prices = self.fetch_current_prices(etf_list)
            
            if not etf_list_with_prices:
                logger.error("无法获取任何ETF价格，分析终止")
                return {"error": "无法获取ETF价格数据"}
            
            # 3. 分析投资组合
            portfolio_analysis = self.analyze_portfolio(etf_list_with_prices)
            
            # 4. 生成策略报告
            strategy_report = self.generate_strategy_report(etf_list_with_prices)
            
            # 5. 生成AI分析
            ai_analysis = self.generate_ai_analysis(
                etf_list_with_prices, 
                portfolio_analysis['portfolio_summary']
            )
            
            # 6. 整合结果
            final_report = {
                'timestamp': datetime.datetime.now().isoformat(),
                'etf_data': etf_list_with_prices,
                'portfolio_analysis': portfolio_analysis,
                'strategy_report': strategy_report,
                'ai_analysis': ai_analysis
            }
            
            # 7. 生成通知消息
            notification_message = self._format_notification_message(final_report)
            
            # 8. 发送通知
            self.send_notification(notification_message)
            
            logger.info("ETF投资机器人运行完成")
            logger.info("=" * 50)
            
            return final_report
        
        except Exception as e:
            logger.error(f"ETF机器人运行过程中出错: {e}")
            return {"error": str(e)}
    
    def _format_notification_message(self, report: Dict[str, Any]) -> str:
        """格式化通知消息"""
        try:
            portfolio_summary = report['portfolio_analysis']['portfolio_summary']
            
            message = f"""
# ETF投资组合分析报告

## 📊 组合概况
- 总市值：{portfolio_summary['总市值']:.2f}元
- 总成本：{portfolio_summary['总成本']:.2f}元  
- 总收益率：{portfolio_summary['总收益率']}
- ETF数量：{portfolio_summary['ETF数量']}只

## 🎯 主要建议
{report['ai_analysis'][:500]}...

## 📅 分析时间
{report['timestamp']}

详细报告请查看系统日志。
"""
            return message
        
        except Exception as e:
            logger.error(f"格式化通知消息失败: {e}")
            return "ETF分析完成，详细信息请查看系统日志。"

def main():
    """主函数"""
    try:
        # 创建ETF机器人实例
        robot = ETFRobot()
        
        # 运行分析
        result = robot.run_analysis()
        
        # 输出结果摘要
        if 'error' not in result:
            print("\n" + "=" * 50)
            print("📈 ETF投资机器人分析完成")
            print("=" * 50)
            
            portfolio_summary = result['portfolio_analysis']['portfolio_summary']
            print(f"💰 总市值：{portfolio_summary['总市值']:.2f}元")
            print(f"📊 总收益率：{portfolio_summary['总收益率']}")
            print(f"📋 ETF数量：{portfolio_summary['ETF数量']}只")
            
            print("\n🤖 AI分析摘要：")
            print(result['ai_analysis'][:200] + "...")
            
            print("\n📄 详细报告已保存到日志文件")
            print("=" * 50)
        else:
            print(f"❌ 分析失败: {result['error']}")
    
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断，程序退出")
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        logger.error(f"程序运行出错: {e}")

if __name__ == "__main__":
    main()
