"""
ETF机器人配置文件
包含API密钥、重试参数等配置信息
支持多个AI模型提供商：OpenAI、千问、火山云
"""
import os
import json
from typing import Dict, Any, Optional

def load_env_file(env_file: str = ".env"):
    """加载.env文件中的环境变量"""
    if not os.path.exists(env_file):
        return

    try:
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip()
                    # 移除引号
                    if value.startswith('"') and value.endswith('"'):
                        value = value[1:-1]
                    elif value.startswith("'") and value.endswith("'"):
                        value = value[1:-1]
                    os.environ[key] = value
    except Exception as e:
        print(f"加载.env文件失败: {e}")

class Config:
    """配置管理类"""

    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or "config/settings.json"
        # 首先加载.env文件
        load_env_file()
        self._load_config()

    def _load_config(self):
        """加载配置文件"""
        # 首先设置默认值
        self._set_defaults()
        
        # 然后尝试从配置文件加载
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                    self._update_from_dict(file_config)
            except Exception as e:
                print(f"加载配置文件失败: {e}，使用默认配置")
        
        # 最后从环境变量覆盖
        self._load_from_env()

    def _set_defaults(self):
        """设置默认配置"""
        # AI模型配置
        self.AI_PROVIDER = 'openai'  # openai, qwen, volcengine

        # OpenAI配置
        self.OPENAI_API_KEY = ''
        self.OPENAI_BASE_URL = 'https://api.openai.com/v1'

        # 千问配置
        self.QWEN_API_KEY = ''
        self.QWEN_MODEL = 'qwen-turbo'

        # 火山云配置
        self.VOLCENGINE_API_KEY = ''
        self.VOLCENGINE_ENDPOINT = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions'
        self.VOLCENGINE_MODEL = 'doubao-lite-4k'

        # Tushare Pro配置
        self.TUSHARE_TOKEN = ''
        self.TUSHARE_PRIMARY = True  # 是否优先使用Tushare

        # 其他配置
        self.SERVER_CHAN_KEY = ''

        # 数据获取配置
        self.MAX_RETRIES = 3
        self.RETRY_DELAY = 1.0
        self.CACHE_TIMEOUT = 300  # 5分钟
        self.HISTORICAL_CACHE_TIMEOUT = 3600  # 1小时

        # ETF分析配置
        self.LOOKBACK_DAYS = 365
        self.RETREAT_THRESHOLDS = [-5, -10, -15]
        self.RISE_THRESHOLDS = [5, 10, 15]

        # AI模型通用配置
        self.AI_TEMPERATURE = 0.3
        self.AI_MAX_RETRIES = 3
        self.AI_MAX_TOKENS = 4000

        # 向后兼容的GPT配置
        self.GPT_MODEL = 'gpt-4o-mini'
        self.GPT_TEMPERATURE = self.AI_TEMPERATURE
        self.GPT_MAX_RETRIES = self.AI_MAX_RETRIES
        
        # AI模型配置映射
        self.AI_MODELS = {
            'openai': {
                'models': ['gpt-4o-mini', 'gpt-4o', 'gpt-3.5-turbo'],
                'default_model': 'gpt-4o-mini',
                'api_key_env': 'OPENAI_API_KEY',
                'display_name': 'OpenAI GPT'
            },
            'qwen': {
                'models': ['qwen3-30b-a3b', 'qwen3-235b-a22b', 'qwen3-32b'],
                'default_model': 'qwen3-30b-a3b',
                'api_key_env': 'QWEN_API_KEY',
                'display_name': '千问 (Qwen)'
            },
            'volcengine': {
                'models': ['doubao-lite-4k', 'doubao-pro-4k', 'doubao-pro-32k'],
                'default_model': 'doubao-lite-4k',
                'api_key_env': 'VOLCENGINE_API_KEY',
                'display_name': '火山云 (豆包)'
            }
        }

        # 文件路径配置
        self.CONFIG_FILE = "config/etf_config.json"
        self.PRICE_HISTORY_FILE = "data/etf_price_history.csv"

        # 数据源配置
        self.DATA_SOURCES = {
            'akshare': {
                'enabled': True,
                'priority': 1,
                'timeout': 30
            }
        }

        # 交易所映射
        self.EXCHANGE_MAPPING = {
            'SH_PREFIXES': ['50', '51', '52', '56', '58'],
            'SZ_PREFIXES': ['15', '16', '17', '18', '19']
        }

        # 价格验证配置
        self.PRICE_VALIDATION = {
            'min_price': 0.1,
            'max_price': 1000.0,
            'required_columns': ['收盘', '开盘', '最高', '最低']
        }

    def _load_from_env(self):
        """从环境变量加载配置"""
        # AI模型配置
        self.AI_PROVIDER = os.getenv('AI_PROVIDER', self.AI_PROVIDER)

        # OpenAI配置
        self.OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', self.OPENAI_API_KEY)
        self.OPENAI_BASE_URL = os.getenv('OPENAI_BASE_URL', self.OPENAI_BASE_URL)

        # 千问配置
        self.QWEN_API_KEY = os.getenv('QWEN_API_KEY', self.QWEN_API_KEY)
        self.QWEN_MODEL = os.getenv('QWEN_MODEL', self.QWEN_MODEL)

        # 火山云配置
        self.VOLCENGINE_API_KEY = os.getenv('VOLCENGINE_API_KEY', self.VOLCENGINE_API_KEY)
        self.VOLCENGINE_ENDPOINT = os.getenv('VOLCENGINE_ENDPOINT', self.VOLCENGINE_ENDPOINT)
        self.VOLCENGINE_MODEL = os.getenv('VOLCENGINE_MODEL', self.VOLCENGINE_MODEL)

        # Tushare Pro配置
        self.TUSHARE_TOKEN = os.getenv('TUSHARE_TOKEN', self.TUSHARE_TOKEN)
        self.TUSHARE_PRIMARY = os.getenv('TUSHARE_PRIMARY', 'true').lower() == 'true'

        # 其他配置
        self.SERVER_CHAN_KEY = os.getenv('SERVER_CHAN_KEY', self.SERVER_CHAN_KEY)

        # 数值配置
        self.MAX_RETRIES = int(os.getenv('MAX_RETRIES', str(self.MAX_RETRIES)))
        self.RETRY_DELAY = float(os.getenv('RETRY_DELAY', str(self.RETRY_DELAY)))
        self.CACHE_TIMEOUT = int(os.getenv('CACHE_TIMEOUT', str(self.CACHE_TIMEOUT)))
        self.HISTORICAL_CACHE_TIMEOUT = int(os.getenv('HISTORICAL_CACHE_TIMEOUT', str(self.HISTORICAL_CACHE_TIMEOUT)))
        self.LOOKBACK_DAYS = int(os.getenv('LOOKBACK_DAYS', str(self.LOOKBACK_DAYS)))
        self.AI_TEMPERATURE = float(os.getenv('AI_TEMPERATURE', str(self.AI_TEMPERATURE)))
        self.AI_MAX_RETRIES = int(os.getenv('AI_MAX_RETRIES', str(self.AI_MAX_RETRIES)))
        self.AI_MAX_TOKENS = int(os.getenv('AI_MAX_TOKENS', str(self.AI_MAX_TOKENS)))
        self.GPT_MODEL = os.getenv('GPT_MODEL', self.GPT_MODEL)

    def _update_from_dict(self, config_dict: Dict[str, Any]):
        """从字典更新配置"""
        for key, value in config_dict.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def save_config(self):
        """保存配置到文件"""
        config_dict = {}
        
        # 只保存非敏感配置到文件
        safe_keys = [
            'AI_PROVIDER', 'OPENAI_BASE_URL', 'QWEN_MODEL', 'VOLCENGINE_ENDPOINT', 
            'VOLCENGINE_MODEL', 'MAX_RETRIES', 'RETRY_DELAY', 'CACHE_TIMEOUT',
            'HISTORICAL_CACHE_TIMEOUT', 'LOOKBACK_DAYS', 'AI_TEMPERATURE',
            'AI_MAX_RETRIES', 'AI_MAX_TOKENS', 'GPT_MODEL', 'RETREAT_THRESHOLDS',
            'RISE_THRESHOLDS', 'AI_MODELS', 'DATA_SOURCES', 'EXCHANGE_MAPPING',
            'PRICE_VALIDATION'
        ]
        
        for key in safe_keys:
            if hasattr(self, key):
                config_dict[key] = getattr(self, key)
        
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置文件失败: {e}")

    def get_ai_config(self, provider: str) -> Dict[str, Any]:
        """获取指定AI提供商的配置"""
        if provider == 'openai':
            return {
                'api_key': self.OPENAI_API_KEY,
                'base_url': self.OPENAI_BASE_URL,
                'model': self.GPT_MODEL,
                'temperature': self.AI_TEMPERATURE,
                'max_tokens': self.AI_MAX_TOKENS
            }
        elif provider == 'qwen':
            return {
                'api_key': self.QWEN_API_KEY,
                'model': self.QWEN_MODEL,
                'temperature': self.AI_TEMPERATURE,
                'max_tokens': self.AI_MAX_TOKENS
            }
        elif provider == 'volcengine':
            return {
                'api_key': self.VOLCENGINE_API_KEY,
                'endpoint': self.VOLCENGINE_ENDPOINT,
                'model': self.VOLCENGINE_MODEL,
                'temperature': self.AI_TEMPERATURE,
                'max_tokens': self.AI_MAX_TOKENS
            }
        else:
            return {}

    def update_ai_config(self, provider: str, config_updates: Dict[str, Any]):
        """更新AI配置"""
        if provider == 'openai':
            if 'api_key' in config_updates:
                self.OPENAI_API_KEY = config_updates['api_key']
            if 'base_url' in config_updates:
                self.OPENAI_BASE_URL = config_updates['base_url']
            if 'model' in config_updates:
                self.GPT_MODEL = config_updates['model']
        elif provider == 'qwen':
            if 'api_key' in config_updates:
                self.QWEN_API_KEY = config_updates['api_key']
            if 'model' in config_updates:
                self.QWEN_MODEL = config_updates['model']
        elif provider == 'volcengine':
            if 'api_key' in config_updates:
                self.VOLCENGINE_API_KEY = config_updates['api_key']
            if 'endpoint' in config_updates:
                self.VOLCENGINE_ENDPOINT = config_updates['endpoint']
            if 'model' in config_updates:
                self.VOLCENGINE_MODEL = config_updates['model']
        
        # 更新通用参数
        if 'temperature' in config_updates:
            self.AI_TEMPERATURE = config_updates['temperature']
            self.GPT_TEMPERATURE = config_updates['temperature']
        if 'max_tokens' in config_updates:
            self.AI_MAX_TOKENS = config_updates['max_tokens']
        
        # 保存配置
        self.save_config()

    def get_available_providers(self) -> Dict[str, str]:
        """获取可用的AI提供商"""
        providers = {}
        
        if self.OPENAI_API_KEY:
            providers['openai'] = self.AI_MODELS['openai']['display_name']
        if self.QWEN_API_KEY:
            providers['qwen'] = self.AI_MODELS['qwen']['display_name']
        if self.VOLCENGINE_API_KEY:
            providers['volcengine'] = self.AI_MODELS['volcengine']['display_name']
            
        return providers

# 全局配置实例
config = Config()
