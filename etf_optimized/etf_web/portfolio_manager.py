#!/usr/bin/env python3
"""
ETF投资组合管理Web界面 - 优化版本
"""
import streamlit as st
import pandas as pd
import json
import os
import sys
from datetime import datetime
from typing import Dict, List, Optional

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from etf_core.data import UnifiedETFDataFetcher, ETFConfig, DataSource
from etf_core.analysis import EnhancedETFAnalyzer, analyze_etfs_enhanced
from etf_core.ai import get_ai_analysis, generate_professional_prompt, multi_ai_helper
from etf_core.strategy import StrategyManager, create_strategy_report
from etf_core.utils import config

# 页面配置
st.set_page_config(
    page_title="ETF投资组合管理器 v2.0",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)

class PortfolioManagerApp:
    """投资组合管理应用"""
    
    def __init__(self):
        self.etf_config = ETFConfig()

        # 使用统一数据获取器
        # 检查Tushare是否可用
        try:
            from etf_core.data.tushare_fetcher import TUSHARE_AVAILABLE
        except ImportError:
            TUSHARE_AVAILABLE = False

        if TUSHARE_AVAILABLE and config.TUSHARE_PRIMARY:
            primary_source = DataSource.TUSHARE
            fallback_sources = [DataSource.AKSHARE]
        else:
            primary_source = DataSource.AKSHARE
            fallback_sources = [DataSource.TUSHARE] if TUSHARE_AVAILABLE else []

        self.data_fetcher = UnifiedETFDataFetcher(
            tushare_token=config.TUSHARE_TOKEN,
            primary_source=primary_source,
            fallback_sources=fallback_sources
        )

        self.analyzer = EnhancedETFAnalyzer()
        self.strategy_manager = StrategyManager()
        
        # 初始化session state
        if 'etf_data' not in st.session_state:
            st.session_state.etf_data = None
        if 'analysis_results' not in st.session_state:
            st.session_state.analysis_results = None
        # 初始化AI分析相关状态
        if 'ai_analysis' not in st.session_state:
            st.session_state.ai_analysis = None
        if 'ai_analysis_provider' not in st.session_state:
            st.session_state.ai_analysis_provider = "AI"
    
    def render_sidebar(self):
        """渲染侧边栏"""
        st.sidebar.title("📈 ETF管理器 v2.0")
        
        # 导航菜单
        page = st.sidebar.selectbox(
            "选择功能",
            ["投资组合管理", "实时分析", "数据监控", "数据源状态", "AI配置", "策略设置", "关于系统"]
        )

        # 显示数据源状态
        st.sidebar.subheader("📊 数据源状态")
        source_stats = self.data_fetcher.get_source_stats()

        for source_name, stats in source_stats.items():
            if stats['total_requests'] > 0:
                status_icon = "✅" if stats['is_available'] else "❌"
                st.sidebar.text(f"{status_icon} {source_name}: {stats['success_rate']:.1f}%")
            else:
                st.sidebar.text(f"⚪ {source_name}: 未使用")
        
        return page
    
    def render_portfolio_management(self):
        """渲染投资组合管理页面"""
        st.title("📊 投资组合管理")
        
        # 当前ETF列表
        etf_list = self.etf_config.etf_list
        
        col1, col2 = st.columns([2, 1])
        
        with col1:
            st.subheader("当前ETF组合")
            if etf_list:
                df = pd.DataFrame(etf_list)
                st.dataframe(df, use_container_width=True)
            else:
                st.info("暂无ETF数据，请添加ETF")
        
        with col2:
            st.subheader("添加新ETF")
            with st.form("add_etf_form"):
                name = st.text_input("ETF名称")
                symbol = st.text_input("ETF代码")
                cost_price = st.number_input("成本价", min_value=0.0, step=0.001, format="%.3f")
                position = st.number_input("持仓数量", min_value=0, step=1)
                sector = st.text_input("所属板块")
                risk_level = st.selectbox("风险等级", ["低", "中", "高"])
                strategy = st.selectbox("投资策略", ["value", "grid", "dca", "momentum"])
                
                if st.form_submit_button("添加ETF"):
                    if name and symbol:
                        new_etf = {
                            "name": name,
                            "symbol": symbol,
                            "cost_price": cost_price,
                            "position": position,
                            "sector": sector,
                            "risk_level": risk_level,
                            "strategy": strategy,
                            "high_price": cost_price,  # 初始设为成本价
                            "target_weight": 10.0
                        }
                        self.etf_config.add_etf(new_etf)
                        st.success(f"已添加 {name}")
                        st.rerun()
                    else:
                        st.error("请填写ETF名称和代码")
        
        # 管理ETF
        if etf_list:
            st.subheader("管理ETF")

            # 选择ETF
            etf_options = [f"{etf['name']} ({etf['symbol']})" for etf in etf_list]
            selected_etf_index = st.selectbox(
                "选择要管理的ETF",
                options=range(len(etf_options)),
                format_func=lambda x: etf_options[x],
                index=None,
                key="manage_etf_selector"
            )

            if selected_etf_index is not None:
                col1, col2 = st.columns(2)

                with col1:
                    if st.button("✏️ 编辑选中的ETF", type="primary"):
                        st.session_state.editing_etf_index = selected_etf_index
                        st.rerun()

                with col2:
                    if st.button("🗑️ 删除选中的ETF", type="secondary"):
                        etf_to_delete = etf_list[selected_etf_index]
                        self.etf_config.remove_etf(etf_to_delete['symbol'])
                        st.success(f"已删除 {etf_to_delete['name']}")
                        st.rerun()

        # 编辑ETF界面
        if hasattr(st.session_state, 'editing_etf_index') and st.session_state.editing_etf_index is not None:
            self.render_edit_etf_form(st.session_state.editing_etf_index)

    def render_edit_etf_form(self, etf_index: int):
        """渲染编辑ETF表单"""
        st.subheader("✏️ 编辑ETF")

        etf_list = self.etf_config.etf_list
        if etf_index >= len(etf_list):
            st.error("ETF索引无效")
            st.session_state.editing_etf_index = None
            return

        current_etf = etf_list[etf_index]

        with st.form("edit_etf_form"):
            st.write(f"正在编辑: **{current_etf['name']} ({current_etf['symbol']})**")

            col1, col2 = st.columns(2)

            with col1:
                name = st.text_input("ETF名称", value=current_etf.get('name', ''))
                symbol = st.text_input("ETF代码", value=current_etf.get('symbol', ''))
                cost_price = st.number_input(
                    "成本价",
                    value=float(current_etf.get('cost_price', 0.0)),
                    min_value=0.0,
                    step=0.001,
                    format="%.3f"
                )
                position = st.number_input(
                    "持仓数量",
                    value=int(current_etf.get('position', 0)),
                    min_value=0,
                    step=1
                )

            with col2:
                sector = st.text_input("所属板块", value=current_etf.get('sector', ''))
                risk_level = st.selectbox(
                    "风险等级",
                    ["低", "中", "高"],
                    index=["低", "中", "高"].index(current_etf.get('risk_level', '中'))
                )
                strategy = st.selectbox(
                    "投资策略",
                    ["value", "grid", "dca", "momentum"],
                    index=["value", "grid", "dca", "momentum"].index(current_etf.get('strategy', 'value'))
                )
                target_weight = st.number_input(
                    "目标权重(%)",
                    value=float(current_etf.get('target_weight', 10.0)),
                    min_value=0.0,
                    max_value=100.0,
                    step=0.1
                )

            col_save, col_cancel = st.columns(2)

            with col_save:
                save_clicked = st.form_submit_button("💾 保存修改", type="primary")

            with col_cancel:
                cancel_clicked = st.form_submit_button("❌ 取消编辑")

            if save_clicked:
                if not all([name, symbol]):
                    st.error("请填写ETF名称和代码")
                else:
                    # 检查代码是否与其他ETF冲突（除了当前编辑的ETF）
                    symbol_conflict = False
                    for i, etf in enumerate(etf_list):
                        if i != etf_index and etf['symbol'] == symbol:
                            symbol_conflict = True
                            break

                    if symbol_conflict:
                        st.error(f"ETF代码 {symbol} 已存在，请使用其他代码")
                    else:
                        updated_etf = {
                            "name": name,
                            "symbol": symbol,
                            "cost_price": cost_price,
                            "position": position,
                            "sector": sector,
                            "risk_level": risk_level,
                            "strategy": strategy,
                            "target_weight": target_weight,
                            "high_price": current_etf.get('high_price', cost_price)  # 保持原有的最高价
                        }

                        if self.etf_config.update_etf_by_index(etf_index, updated_etf):
                            st.success(f"✅ 已成功更新 {name}")
                            st.session_state.editing_etf_index = None
                            st.rerun()
                        else:
                            st.error("❌ 更新失败")

            if cancel_clicked:
                st.session_state.editing_etf_index = None
                st.rerun()

    def render_realtime_analysis(self):
        """渲染实时分析页面"""
        st.title("📊 实时分析")
        
        etf_list = self.etf_config.etf_list
        
        if not etf_list:
            st.warning("请先在投资组合管理页面添加ETF")
            return
        
        col1, col2 = st.columns([1, 1])
        
        with col1:
            if st.button("🔄 获取实时数据", type="primary"):
                # 创建进度条和状态显示
                progress_bar = st.progress(0)
                status_text = st.empty()
                result_container = st.empty()

                try:
                    # 获取ETF代码列表
                    symbols = [etf['symbol'] for etf in etf_list]
                    total_count = len(symbols)

                    status_text.text(f"开始获取 {total_count} 个ETF的实时数据...")

                    # 定义进度回调函数
                    def progress_callback(current, total, symbol, price):
                        progress = current / total
                        progress_bar.progress(progress)

                        if price is not None:
                            status_text.text(f"✅ {symbol}: {price:.3f}元 ({current}/{total})")
                        else:
                            status_text.text(f"❌ {symbol}: 获取失败 ({current}/{total})")

                    # 批量获取价格
                    price_results = self.data_fetcher.get_batch_prices_with_progress(
                        symbols, progress_callback
                    )

                    # 处理结果
                    updated_etfs = []
                    failed_etfs = []

                    for etf in etf_list:
                        symbol = etf['symbol']
                        current_price = price_results.get(symbol)

                        if current_price is not None:
                            etf_copy = etf.copy()
                            etf_copy['current_price'] = current_price
                            updated_etfs.append(etf_copy)
                        else:
                            failed_etfs.append(etf['name'])

                    # 保存结果
                    st.session_state.etf_data = updated_etfs
                    st.session_state.failed_symbols = [etf['symbol'] for etf in etf_list if etf['symbol'] not in [e['symbol'] for e in updated_etfs]]

                    # 分析数据
                    if updated_etfs:
                        analysis_df = analyze_etfs_enhanced(updated_etfs)
                        st.session_state.analysis_results = analysis_df

                    # 显示结果摘要
                    success_count = len(updated_etfs)
                    failed_count = len(failed_etfs)

                    progress_bar.progress(1.0)

                    if success_count > 0:
                        status_text.success(f"✅ 成功获取 {success_count}/{total_count} 个ETF的数据")

                        if failed_count > 0:
                            with result_container.expander(f"⚠️ {failed_count} 个ETF获取失败", expanded=False):
                                st.write("以下ETF获取失败，可能的原因：")
                                st.write("• 网络连接问题")
                                st.write("• ETF代码错误")
                                st.write("• 数据源暂时不可用")
                                st.write("• ETF已停牌或退市")
                                st.write("")
                                st.write("失败的ETF:")
                                for name in failed_etfs:
                                    st.write(f"• {name}")
                                st.write("")
                                st.info("💡 建议：检查ETF代码是否正确，或稍后重试")
                    else:
                        status_text.error("❌ 所有ETF数据获取失败，请检查网络连接或稍后重试")

                except Exception as e:
                    progress_bar.progress(0)
                    status_text.error(f"❌ 获取数据时发生错误: {str(e)}")
                    st.error("数据获取过程中出现异常，请稍后重试")
        
        with col2:
            # AI配置区域
            st.subheader("🤖 AI智能分析配置")

            # 获取可用的AI提供商
            available_providers = multi_ai_helper.get_available_providers()

            if not available_providers:
                st.warning("⚠️ 没有可用的AI模型，请先配置API密钥")
                st.info("💡 请前往 'AI配置' 页面配置API密钥")
            else:
                # AI提供商选择
                col_ai1, col_ai2 = st.columns([1, 1])

                with col_ai1:
                    # 获取当前提供商
                    current_provider = getattr(config, 'AI_PROVIDER', 'openai')
                    provider_options = list(available_providers.keys())

                    if current_provider in provider_options:
                        default_provider_index = provider_options.index(current_provider)
                    else:
                        default_provider_index = 0

                    selected_provider = st.selectbox(
                        "选择AI提供商",
                        options=provider_options,
                        format_func=lambda x: available_providers[x],
                        index=default_provider_index,
                        help="选择用于分析的AI模型提供商",
                        key="ai_provider_select"
                    )

                with col_ai2:
                    # 模型选择
                    if selected_provider in config.AI_MODELS:
                        model_config = config.AI_MODELS[selected_provider]
                        available_models = model_config['models']

                        # 获取当前模型
                        if selected_provider == 'openai':
                            current_model = getattr(config, 'GPT_MODEL', model_config['default_model'])
                        elif selected_provider == 'qwen':
                            current_model = getattr(config, 'QWEN_MODEL', model_config['default_model'])
                        elif selected_provider == 'volcengine':
                            current_model = getattr(config, 'VOLCENGINE_MODEL', model_config['default_model'])
                        else:
                            current_model = model_config['default_model']

                        if current_model in available_models:
                            model_default_index = available_models.index(current_model)
                        else:
                            model_default_index = available_models.index(model_config['default_model'])

                        selected_model = st.selectbox(
                            f"选择{available_providers[selected_provider]}模型",
                            options=available_models,
                            index=model_default_index,
                            help=f"选择具体的{available_providers[selected_provider]}模型",
                            key="ai_model_select"
                        )
                    else:
                        selected_model = "默认模型"

                # 显示当前配置
                st.info(f"🎯 当前配置: {available_providers[selected_provider]} - {selected_model}")

            col2_1, col2_2 = st.columns([1, 1])

            with col2_1:
                # AI智能分析按钮
                ai_analysis_disabled = not available_providers
                if st.button("🤖 AI智能分析", disabled=ai_analysis_disabled):
                    if st.session_state.etf_data:
                        with st.spinner(f"正在使用{available_providers.get(selected_provider, 'AI')}进行分析..."):
                            try:
                                # 计算组合摘要
                                etf_data = st.session_state.etf_data
                                total_value = sum(etf.get('current_price', 0) * etf.get('position', 0) for etf in etf_data)
                                total_cost = sum(etf.get('cost_price', 0) * etf.get('position', 0) for etf in etf_data)

                                portfolio_summary = {
                                    '总市值': total_value,
                                    '总成本': total_cost,
                                    '总收益率': f"{((total_value - total_cost) / total_cost * 100):.2f}%" if total_cost > 0 else "0%",
                                    'ETF数量': len(etf_data)
                                }

                                # 生成AI分析，使用用户选择的提供商
                                prompt = generate_professional_prompt(etf_data, portfolio_summary)
                                ai_analysis = get_ai_analysis(prompt, provider=selected_provider)

                                if ai_analysis and "暂时不可用" not in ai_analysis:
                                    st.session_state.ai_analysis = ai_analysis
                                    st.session_state.ai_analysis_provider = available_providers[selected_provider]
                                    st.success(f"✅ {available_providers[selected_provider]}分析完成")
                                else:
                                    st.error(f"❌ {available_providers[selected_provider]}分析失败，请检查API配置或网络连接")

                            except Exception as e:
                                st.error(f"AI分析过程中出错: {str(e)}")
                    else:
                        st.warning("请先获取实时数据")

            with col2_2:
                if st.button("🔄 重试失败项"):
                    if hasattr(st.session_state, 'failed_symbols') and st.session_state.failed_symbols:
                        with st.spinner("重试获取失败的ETF数据..."):
                            retry_results = self.data_fetcher.get_batch_prices(st.session_state.failed_symbols)

                            # 更新成功的数据
                            if st.session_state.etf_data:
                                updated_count = 0
                                for etf in st.session_state.etf_data:
                                    symbol = etf['symbol']
                                    if symbol in retry_results and retry_results[symbol] is not None:
                                        etf['current_price'] = retry_results[symbol]
                                        updated_count += 1

                                if updated_count > 0:
                                    st.success(f"重试成功，更新了 {updated_count} 个ETF的数据")
                                    # 重新分析
                                    analysis_df = analyze_etfs_enhanced(st.session_state.etf_data)
                                    st.session_state.analysis_results = analysis_df
                                else:
                                    st.warning("重试未获取到新数据")
                    else:
                        st.info("没有失败的ETF需要重试")
        
        # 显示分析结果
        if st.session_state.analysis_results is not None:
            st.subheader("📈 分析结果")
            st.dataframe(st.session_state.analysis_results, use_container_width=True)
        
        # 显示AI分析
        if hasattr(st.session_state, 'ai_analysis'):
            # 显示分析标题和提供商信息
            provider_info = getattr(st.session_state, 'ai_analysis_provider', 'AI')
            st.subheader(f"🤖 AI智能分析 - {provider_info}")

            # 显示分析结果
            st.markdown(st.session_state.ai_analysis)

    def render_data_monitoring(self):
        """渲染数据监控页面"""
        st.title("📊 数据获取监控")

        # 检查是否有监控数据
        if hasattr(self.data_fetcher, 'monitor'):
            monitor = self.data_fetcher.monitor

            # 全局统计
            st.subheader("📈 全局统计")
            global_stats = monitor.get_global_stats()

            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("总请求数", global_stats['total_requests'])
            with col2:
                st.metric("成功率", f"{global_stats['success_rate']:.1f}%")
            with col3:
                st.metric("平均响应时间", f"{global_stats['avg_response_time']:.2f}s")
            with col4:
                st.metric("运行时间", f"{global_stats['uptime_seconds']/3600:.1f}h")

            # 问题ETF
            st.subheader("⚠️ 问题ETF")
            problematic = monitor.get_problematic_symbols()

            if problematic:
                st.warning(f"发现 {len(problematic)} 个有问题的ETF")

                problem_data = []
                for symbol in problematic:
                    stats = monitor.get_symbol_stats(symbol)
                    problem_data.append({
                        'ETF代码': symbol,
                        '总尝试': stats['total_attempts'],
                        '成功次数': stats['successful_attempts'],
                        '失败次数': stats['failed_attempts'],
                        '成功率': f"{stats['success_rate']:.1f}%",
                        '连续失败': stats['consecutive_failures'],
                        '平均响应时间': f"{stats['avg_response_time']:.2f}s"
                    })

                st.dataframe(pd.DataFrame(problem_data), use_container_width=True)

                # 重置选项
                if st.button("🔄 重置问题ETF统计"):
                    for symbol in problematic:
                        monitor.reset_symbol_stats(symbol)
                    st.success("已重置问题ETF的统计信息")
                    st.rerun()
            else:
                st.success("✅ 没有发现问题ETF")

            # 最近失败记录
            st.subheader("🔍 最近失败记录")
            recent_failures = monitor.get_recent_failures(minutes=30)

            if recent_failures:
                st.warning(f"最近30分钟内有 {len(recent_failures)} 次失败")

                failure_data = []
                for record in recent_failures[-10:]:  # 显示最近10条
                    failure_data.append({
                        '时间': datetime.fromtimestamp(record['timestamp']).strftime('%H:%M:%S'),
                        'ETF代码': record['symbol'],
                        '响应时间': f"{record['response_time']:.2f}s",
                        '错误信息': record['error'][:50] + '...' if record['error'] and len(record['error']) > 50 else record['error']
                    })

                st.dataframe(pd.DataFrame(failure_data), use_container_width=True)
            else:
                st.success("✅ 最近30分钟内没有失败记录")

            # 性能摘要
            st.subheader("📊 性能摘要")
            performance = monitor.get_performance_summary()

            col1, col2 = st.columns(2)

            with col1:
                st.write("**最慢的ETF:**")
                if performance['slowest_symbols']:
                    for item in performance['slowest_symbols']:
                        st.write(f"• {item['symbol']}: {item['avg_response_time']:.2f}s")
                else:
                    st.write("暂无数据")

            with col2:
                st.write("**监控统计:**")
                st.write(f"• 跟踪的ETF数量: {performance['total_symbols_tracked']}")
                st.write(f"• 最近失败次数: {performance['recent_failures_count']}")
                st.write(f"• 问题ETF数量: {len(performance['problematic_symbols'])}")

            # 操作按钮
            st.subheader("🔧 监控操作")
            col1, col2, col3 = st.columns(3)

            with col1:
                if st.button("📤 导出统计数据"):
                    stats_data = monitor.export_stats()
                    st.download_button(
                        label="下载统计数据",
                        data=json.dumps(stats_data, indent=2, ensure_ascii=False),
                        file_name=f"etf_monitor_stats_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                        mime="application/json"
                    )

            with col2:
                if st.button("🗑️ 清空统计数据"):
                    monitor.clear_all_stats()
                    st.success("已清空所有监控统计数据")
                    st.rerun()

            with col3:
                if st.button("🔄 刷新数据"):
                    st.rerun()

        else:
            st.info("数据监控功能未启用")
            st.write("要启用监控功能，请在数据获取器中集成监控模块。")

    def render_data_source_status(self):
        """渲染数据源状态页面"""
        st.title("🔗 数据源状态")

        # 获取数据源统计
        source_stats = self.data_fetcher.get_source_stats()

        # 总体状态
        st.subheader("📊 总体状态")

        col1, col2, col3 = st.columns(3)

        total_requests = sum(stats['total_requests'] for stats in source_stats.values())
        total_success = sum(stats['success_count'] for stats in source_stats.values())
        available_sources = sum(1 for stats in source_stats.values() if stats['is_available'])

        with col1:
            st.metric("总请求数", total_requests)
        with col2:
            overall_success_rate = (total_success / total_requests * 100) if total_requests > 0 else 0
            st.metric("总体成功率", f"{overall_success_rate:.1f}%")
        with col3:
            st.metric("可用数据源", f"{available_sources}/{len(source_stats)}")

        # 各数据源详细状态
        st.subheader("📈 各数据源状态")

        for source_name, stats in source_stats.items():
            with st.expander(f"📊 {source_name.upper()} 数据源", expanded=True):

                # 状态指示器
                if stats['is_available']:
                    st.success(f"✅ {source_name} 数据源正常运行")
                else:
                    st.error(f"❌ {source_name} 数据源不可用")

                # 统计信息
                col1, col2, col3, col4 = st.columns(4)

                with col1:
                    st.metric("总请求", stats['total_requests'])
                with col2:
                    st.metric("成功次数", stats['success_count'])
                with col3:
                    st.metric("失败次数", stats['failure_count'])
                with col4:
                    st.metric("成功率", f"{stats['success_rate']:.1f}%")

                # 连续失败警告
                if stats['consecutive_failures'] > 0:
                    st.warning(f"⚠️ 连续失败 {stats['consecutive_failures']} 次")

                # 最后成功/失败时间
                if stats['last_success_time']:
                    last_success = datetime.fromtimestamp(stats['last_success_time'])
                    st.info(f"🕐 最后成功时间: {last_success.strftime('%Y-%m-%d %H:%M:%S')}")

                if stats['last_failure_time']:
                    last_failure = datetime.fromtimestamp(stats['last_failure_time'])
                    st.warning(f"🕐 最后失败时间: {last_failure.strftime('%Y-%m-%d %H:%M:%S')}")

                # 重置按钮
                if st.button(f"🔄 重置 {source_name} 统计", key=f"reset_{source_name}"):
                    # 这里需要实现重置功能
                    st.success(f"已重置 {source_name} 数据源统计")
                    st.rerun()

        # 数据源配置
        st.subheader("⚙️ 数据源配置")

        # Tushare Pro配置状态
        st.write("**Tushare Pro 配置:**")
        if config.TUSHARE_TOKEN:
            st.success("✅ Tushare Pro Token 已配置")
            st.info(f"Token: {config.TUSHARE_TOKEN[:10]}...")
        else:
            st.warning("⚠️ Tushare Pro Token 未配置")
            st.write("请在环境变量中设置 TUSHARE_TOKEN")

        # 主要数据源设置
        st.write("**主要数据源:**")
        primary_source = "Tushare Pro" if config.TUSHARE_PRIMARY else "AkShare"
        st.info(f"当前主要数据源: {primary_source}")

        # 配置建议
        st.subheader("💡 配置建议")

        if not config.TUSHARE_TOKEN:
            st.warning("""
            **建议配置 Tushare Pro:**
            1. 访问 https://tushare.pro/ 注册账号
            2. 获取API Token
            3. 在环境变量中设置 TUSHARE_TOKEN
            4. Tushare Pro 提供更稳定的数据服务
            """)

        # 测试连接
        st.subheader("🔧 连接测试")

        test_symbol = st.text_input("测试ETF代码", value="510300", help="输入ETF代码进行连接测试")

        col1, col2 = st.columns(2)

        with col1:
            if st.button("🧪 测试 Tushare Pro"):
                if config.TUSHARE_TOKEN:
                    with st.spinner("测试 Tushare Pro 连接..."):
                        try:
                            from etf_core.data import TushareProFetcher
                            fetcher = TushareProFetcher(config.TUSHARE_TOKEN)
                            price = fetcher.get_current_price(test_symbol)

                            if price:
                                st.success(f"✅ Tushare Pro 连接成功: {test_symbol} = {price:.3f}元")
                            else:
                                st.error("❌ Tushare Pro 连接失败")
                        except Exception as e:
                            st.error(f"❌ Tushare Pro 测试失败: {e}")
                else:
                    st.error("❌ 请先配置 Tushare Pro Token")

        with col2:
            if st.button("🧪 测试 AkShare"):
                with st.spinner("测试 AkShare 连接..."):
                    try:
                        from etf_core.data import ETFDataFetcher
                        fetcher = ETFDataFetcher()
                        price = fetcher.get_current_price(test_symbol)

                        if price:
                            st.success(f"✅ AkShare 连接成功: {test_symbol} = {price:.3f}元")
                        else:
                            st.error("❌ AkShare 连接失败")
                    except Exception as e:
                        st.error(f"❌ AkShare 测试失败: {e}")

        # 操作按钮
        st.subheader("🔧 数据源操作")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("🔄 刷新状态"):
                st.rerun()

        with col2:
            if st.button("🗑️ 重置所有统计"):
                self.data_fetcher.reset_source_stats()
                st.success("已重置所有数据源统计")
                st.rerun()

        with col3:
            if st.button("🧹 清空缓存"):
                self.data_fetcher.clear_cache()
                st.success("已清空所有数据源缓存")
                st.rerun()
    
    def render_ai_config(self):
        """渲染AI配置页面"""
        st.title("🤖 AI模型配置")
        
        # 显示可用的AI提供商
        available_providers = multi_ai_helper.get_available_providers()
        
        st.subheader("可用的AI模型")
        if available_providers:
            for provider, name in available_providers.items():
                st.success(f"✅ {name} ({provider})")
        else:
            st.warning("⚠️ 没有可用的AI模型，请配置API密钥")
        
        # AI配置表单
        st.subheader("配置AI模型")
        
        tab1, tab2, tab3 = st.tabs(["OpenAI", "千问", "火山云"])
        
        with tab1:
            st.text_input("OpenAI API Key", type="password", key="openai_key")
            st.text_input("Base URL", value="https://api.openai.com/v1", key="openai_url")
            st.selectbox("模型", ["gpt-4o-mini", "gpt-4o", "gpt-3.5-turbo"], key="openai_model")
        
        with tab2:
            st.text_input("千问 API Key", type="password", key="qwen_key")
            st.selectbox("模型", ["qwen-turbo", "qwen-plus", "qwen-max", "qwen2.5-72b-instruct"], key="qwen_model")
        
        with tab3:
            st.text_input("火山云 API Key", type="password", key="volcengine_key")
            st.text_input("Endpoint", key="volcengine_endpoint")
            st.selectbox("模型", ["doubao-lite-4k", "doubao-pro-4k"], key="volcengine_model")
        
        # 通用参数
        st.subheader("通用参数")
        col1, col2 = st.columns(2)
        with col1:
            st.slider("Temperature", 0.0, 1.0, 0.3, 0.1, key="ai_temperature")
        with col2:
            st.number_input("Max Tokens", 1000, 8000, 4000, 100, key="ai_max_tokens")
        
        if st.button("保存配置"):
            st.success("配置已保存")
    
    def render_strategy_settings(self):
        """渲染策略设置页面"""
        st.title("⚙️ 策略设置")
        
        # 策略配置
        strategy_settings = self.strategy_manager.settings
        
        # 全局设置
        st.subheader("全局设置")
        col1, col2 = st.columns(2)
        
        with col1:
            max_position = st.slider(
                "单个ETF最大仓位比例", 
                0.1, 0.5, 
                strategy_settings["global_settings"]["max_position_per_etf"],
                0.05
            )
            cash_reserve = st.slider(
                "现金储备比例", 
                0.0, 0.3, 
                strategy_settings["global_settings"]["cash_reserve_ratio"],
                0.05
            )
        
        with col2:
            rebalance_threshold = st.slider(
                "再平衡阈值", 
                0.01, 0.1, 
                strategy_settings["global_settings"]["rebalance_threshold"],
                0.01
            )
            risk_tolerance = st.selectbox(
                "风险承受能力",
                ["low", "medium", "high"],
                index=["low", "medium", "high"].index(strategy_settings["global_settings"]["risk_tolerance"])
            )
        
        # 网格策略设置
        st.subheader("网格策略")
        grid_enabled = st.checkbox("启用网格策略", strategy_settings["grid_strategy"]["enabled"])
        
        if grid_enabled:
            col1, col2 = st.columns(2)
            with col1:
                buy_levels = st.text_input(
                    "买入点位 (用逗号分隔)", 
                    ",".join(map(str, strategy_settings["grid_strategy"]["buy_levels"]))
                )
            with col2:
                sell_levels = st.text_input(
                    "卖出点位 (用逗号分隔)", 
                    ",".join(map(str, strategy_settings["grid_strategy"]["sell_levels"]))
                )
        
        if st.button("保存策略设置"):
            # 更新设置
            new_settings = {
                "max_position_per_etf": max_position,
                "cash_reserve_ratio": cash_reserve,
                "rebalance_threshold": rebalance_threshold,
                "risk_tolerance": risk_tolerance
            }
            self.strategy_manager.update_strategy_settings("global_settings", new_settings)
            st.success("策略设置已保存")
    
    def render_about(self):
        """渲染关于页面"""
        st.title("ℹ️ 关于系统")
        
        st.markdown("""
        ## ETF投资组合管理系统 v2.0
        
        ### 🌟 主要特性
        - 📊 实时ETF数据获取和分析
        - 🤖 多AI模型智能投资建议
        - 📈 多种投资策略支持
        - 🌐 直观的Web管理界面
        - 📱 微信通知推送
        
        ### 🏗️ 技术架构
        - **前端**: Streamlit
        - **数据源**: akshare
        - **AI模型**: OpenAI GPT, 千问, 火山云
        - **分析引擎**: 自研多策略分析框架
        
        ### 📞 支持
        - 项目地址: [GitHub](https://github.com/your-repo)
        - 文档: [使用指南](https://docs.your-site.com)
        - 问题反馈: [Issues](https://github.com/your-repo/issues)
        
        ### ⚠️ 免责声明
        本系统仅供学习和研究使用，不构成投资建议。投资有风险，入市需谨慎。
        """)
    
    def run(self):
        """运行应用"""
        # 渲染侧边栏
        page = self.render_sidebar()
        
        # 根据选择的页面渲染内容
        if page == "投资组合管理":
            self.render_portfolio_management()
        elif page == "实时分析":
            self.render_realtime_analysis()
        elif page == "数据监控":
            self.render_data_monitoring()
        elif page == "数据源状态":
            self.render_data_source_status()
        elif page == "AI配置":
            self.render_ai_config()
        elif page == "策略设置":
            self.render_strategy_settings()
        elif page == "关于系统":
            self.render_about()

def main():
    """主函数"""
    app = PortfolioManagerApp()
    app.run()

if __name__ == "__main__":
    main()
