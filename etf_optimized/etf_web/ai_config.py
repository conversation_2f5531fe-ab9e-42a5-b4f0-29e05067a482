"""
AI配置页面模块
"""
import streamlit as st
import os
from typing import Dict, Any

from etf_core.utils import config
from etf_core.ai import multi_ai_helper

class AIConfigPage:
    """AI配置页面类"""
    
    def __init__(self):
        self.config = config
    
    def render_provider_status(self):
        """渲染AI提供商状态"""
        st.subheader("🤖 AI模型状态")
        
        available_providers = multi_ai_helper.get_available_providers()
        
        if available_providers:
            for provider, display_name in available_providers.items():
                st.success(f"✅ {display_name} - 已配置")
        else:
            st.warning("⚠️ 没有可用的AI模型，请配置API密钥")
        
        # 显示当前使用的提供商
        current_provider = getattr(self.config, 'AI_PROVIDER', 'openai')
        if current_provider in available_providers:
            st.info(f"🎯 当前使用: {available_providers[current_provider]}")
    
    def render_openai_config(self):
        """渲染OpenAI配置"""
        st.subheader("🔵 OpenAI 配置")
        
        with st.form("openai_config"):
            api_key = st.text_input(
                "API Key", 
                value=self.config.OPENAI_API_KEY[:10] + "..." if self.config.OPENAI_API_KEY else "",
                type="password",
                help="OpenAI API密钥"
            )
            
            base_url = st.text_input(
                "Base URL",
                value=self.config.OPENAI_BASE_URL,
                help="API基础URL，通常不需要修改"
            )
            
            model = st.selectbox(
                "模型",
                options=["gpt-4o-mini", "gpt-4o", "gpt-3.5-turbo"],
                index=0 if self.config.GPT_MODEL == "gpt-4o-mini" else 1 if self.config.GPT_MODEL == "gpt-4o" else 2,
                help="选择要使用的GPT模型"
            )
            
            if st.form_submit_button("保存OpenAI配置"):
                if api_key and not api_key.endswith("..."):
                    # 更新配置
                    updates = {
                        'api_key': api_key,
                        'base_url': base_url,
                        'model': model
                    }
                    self.config.update_ai_config('openai', updates)
                    st.success("OpenAI配置已保存")
                    st.rerun()
                else:
                    st.error("请输入有效的API Key")
    
    def render_qwen_config(self):
        """渲染千问配置"""
        st.subheader("🟡 千问 (Qwen) 配置")
        
        with st.form("qwen_config"):
            api_key = st.text_input(
                "API Key",
                value=self.config.QWEN_API_KEY[:10] + "..." if self.config.QWEN_API_KEY else "",
                type="password",
                help="千问API密钥"
            )
            
            model = st.selectbox(
                "模型",
                options=["qwen3-30b-a3b", "qwen3-235b-a22b", "qwen3-32b"],
                index=0 if self.config.QWEN_MODEL == "qwen3-30b-a3b" else 1 if self.config.QWEN_MODEL == "qwen3-235b-a22b" else 2,
                help="选择要使用的千问模型"
            )
            
            if st.form_submit_button("保存千问配置"):
                if api_key and not api_key.endswith("..."):
                    updates = {
                        'api_key': api_key,
                        'model': model
                    }
                    self.config.update_ai_config('qwen', updates)
                    st.success("千问配置已保存")
                    st.rerun()
                else:
                    st.error("请输入有效的API Key")
    
    def render_volcengine_config(self):
        """渲染火山云配置"""
        st.subheader("🔴 火山云 (豆包) 配置")
        
        with st.form("volcengine_config"):
            api_key = st.text_input(
                "API Key",
                value=self.config.VOLCENGINE_API_KEY[:10] + "..." if self.config.VOLCENGINE_API_KEY else "",
                type="password",
                help="火山云API密钥"
            )
            
            endpoint = st.text_input(
                "Endpoint",
                value=self.config.VOLCENGINE_ENDPOINT,
                help="火山云API端点"
            )
            
            model = st.selectbox(
                "模型",
                options=["doubao-seed-1-6-250615", "doubao-seed-1-6-thinking-250715", "doubao-pro-32k"],
                index=0,
                help="选择要使用的豆包模型"
            )
            
            if st.form_submit_button("保存火山云配置"):
                if api_key and not api_key.endswith("..."):
                    updates = {
                        'api_key': api_key,
                        'endpoint': endpoint,
                        'model': model
                    }
                    self.config.update_ai_config('volcengine', updates)
                    st.success("火山云配置已保存")
                    st.rerun()
                else:
                    st.error("请输入有效的API Key")
    
    def render_general_settings(self):
        """渲染通用设置"""
        st.subheader("⚙️ 通用设置")
        
        col1, col2 = st.columns(2)
        
        with col1:
            temperature = st.slider(
                "Temperature",
                min_value=0.0,
                max_value=1.0,
                value=self.config.AI_TEMPERATURE,
                step=0.1,
                help="控制AI回答的随机性，值越高越随机"
            )
            
            max_tokens = st.number_input(
                "Max Tokens",
                min_value=1000,
                max_value=8000,
                value=self.config.AI_MAX_TOKENS,
                step=100,
                help="AI回答的最大长度"
            )
        
        with col2:
            max_retries = st.number_input(
                "最大重试次数",
                min_value=1,
                max_value=10,
                value=self.config.AI_MAX_RETRIES,
                help="API调用失败时的最大重试次数"
            )
            
            provider = st.selectbox(
                "默认AI提供商",
                options=["openai", "qwen", "volcengine"],
                index=["openai", "qwen", "volcengine"].index(self.config.AI_PROVIDER),
                help="选择默认使用的AI提供商"
            )
        
        if st.button("保存通用设置"):
            # 更新通用设置
            self.config.AI_TEMPERATURE = temperature
            self.config.AI_MAX_TOKENS = max_tokens
            self.config.AI_MAX_RETRIES = max_retries
            self.config.AI_PROVIDER = provider
            self.config.save_config()
            st.success("通用设置已保存")
    
    def render_test_connection(self):
        """渲染连接测试"""
        st.subheader("🔧 连接测试")
        
        test_provider = st.selectbox(
            "选择要测试的AI提供商",
            options=list(multi_ai_helper.get_available_providers().keys()),
            format_func=lambda x: multi_ai_helper.get_available_providers()[x]
        )
        
        if st.button("测试连接"):
            with st.spinner(f"正在测试 {multi_ai_helper.get_available_providers()[test_provider]} 连接..."):
                try:
                    test_prompt = "请简单回复'连接测试成功'"
                    response = multi_ai_helper.call_ai(test_prompt, test_provider)
                    
                    if response:
                        st.success(f"✅ {multi_ai_helper.get_available_providers()[test_provider]} 连接成功")
                        st.info(f"回复: {response}")
                    else:
                        st.error(f"❌ {multi_ai_helper.get_available_providers()[test_provider]} 连接失败")
                
                except Exception as e:
                    st.error(f"❌ 连接测试失败: {str(e)}")
    
    def render_server_chan_config(self):
        """渲染Server酱配置"""
        st.subheader("📱 Server酱通知配置")
        
        with st.form("server_chan_config"):
            server_chan_key = st.text_input(
                "Server酱 Key",
                value=self.config.SERVER_CHAN_KEY[:10] + "..." if self.config.SERVER_CHAN_KEY else "",
                type="password",
                help="Server酱推送密钥，用于微信通知"
            )
            
            if st.form_submit_button("保存Server酱配置"):
                if server_chan_key and not server_chan_key.endswith("..."):
                    self.config.SERVER_CHAN_KEY = server_chan_key
                    self.config.save_config()
                    st.success("Server酱配置已保存")
                else:
                    st.error("请输入有效的Server酱 Key")
    
    def render(self):
        """渲染AI配置页面"""
        st.title("🤖 AI模型配置")
        
        # 显示状态
        self.render_provider_status()
        
        st.divider()
        
        # 配置选项卡
        tab1, tab2, tab3, tab4, tab5 = st.tabs(["OpenAI", "千问", "火山云", "通用设置", "其他配置"])
        
        with tab1:
            self.render_openai_config()
        
        with tab2:
            self.render_qwen_config()
        
        with tab3:
            self.render_volcengine_config()
        
        with tab4:
            self.render_general_settings()
            st.divider()
            self.render_test_connection()
        
        with tab5:
            self.render_server_chan_config()
        
        # 使用说明
        with st.expander("📖 使用说明"):
            st.markdown("""
            ### API密钥获取方式
            
            **OpenAI:**
            1. 访问 [OpenAI Platform](https://platform.openai.com/)
            2. 注册账号并登录
            3. 在API Keys页面创建新的API密钥
            
            **千问 (Qwen):**
            1. 访问 [阿里云百炼](https://bailian.console.aliyun.com/)
            2. 开通服务并获取API密钥
            
            **火山云 (豆包):**
            1. 访问 [火山引擎](https://console.volcengine.com/)
            2. 开通豆包服务并获取API密钥
            
            **Server酱:**
            1. 访问 [Server酱官网](https://sct.ftqq.com/)
            2. 微信扫码登录并获取推送密钥
            
            ### 注意事项
            - API密钥请妥善保管，不要泄露给他人
            - 建议配置多个AI提供商作为备用
            - 定期检查API使用量和费用
            """)

def main():
    """主函数"""
    ai_config = AIConfigPage()
    ai_config.render()

if __name__ == "__main__":
    main()
