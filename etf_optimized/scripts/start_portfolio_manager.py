#!/usr/bin/env python3
"""
ETF投资组合管理器启动脚本 - 优化版本
"""
import os
import sys
import subprocess

def check_port(port):
    """检查端口是否被占用"""
    import socket
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) == 0

def find_available_port(start_port=8501):
    """找到可用端口"""
    port = start_port
    while check_port(port) and port < start_port + 10:
        port += 1
    return port

def main():
    """启动投资组合管理器"""
    print("🚀 启动ETF投资组合管理器 v2.0...")
    print("=" * 50)

    # 检查端口占用
    default_port = 8501
    if check_port(default_port):
        available_port = find_available_port(default_port + 1)
        print(f"⚠️  端口 {default_port} 已被占用")
        print(f"🔄 使用端口 {available_port}")
        port = available_port
    else:
        port = default_port

    # 检查依赖
    try:
        import streamlit
        print("✅ Streamlit 已安装")
    except ImportError:
        print("❌ Streamlit 未安装，正在安装...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "streamlit"])
        print("✅ Streamlit 安装完成")

    # 检查AI模型依赖
    ai_deps = [
        ("openai", "OpenAI"),
        ("dashscope", "千问"),
        ("volcengine", "火山云")
    ]

    for module, name in ai_deps:
        try:
            __import__(module)
            print(f"✅ {name} SDK 已安装")
        except ImportError:
            print(f"⚠️ {name} SDK 未安装，部分AI功能可能不可用")

    # 检查AI配置
    print("\n🤖 AI模型配置检查:")
    try:
        from etf_optimized.etf_core.ai import multi_ai_helper
        available_providers = multi_ai_helper.get_available_providers()

        if available_providers:
            print(f"✅ 可用AI模型: {', '.join(available_providers.values())}")
        else:
            print("⚠️ 没有可用的AI模型，请配置API密钥")
            print("💡 可在Web界面的AI配置页面进行配置")
    except Exception as e:
        print(f"⚠️ AI配置检查失败: {e}")
    
    # 获取当前目录
    current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    manager_file = os.path.join(current_dir, "etf_web", "portfolio_manager.py")
    
    # 启动Streamlit应用
    print(f"📂 工作目录: {current_dir}")
    print(f"📄 管理器文件: {manager_file}")
    print("\n🌐 正在启动Web界面...")
    print("💡 提示：浏览器将自动打开管理界面")
    print(f"🔗 如果浏览器未自动打开，请访问: http://localhost:{port}")
    print("🔧 AI模型配置已集成到主界面，可在导航菜单中找到")
    print("\n⏹️  按 Ctrl+C 停止服务")
    print("=" * 50)

    try:
        # 启动streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run",
            manager_file,
            "--server.port", str(port),
            "--server.address", "localhost",
            "--browser.gatherUsageStats", "false"
        ])
    except KeyboardInterrupt:
        print("\n\n⏹️  ETF投资组合管理器已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("💡 请检查Python环境和依赖包是否正确安装")

if __name__ == "__main__":
    main()
