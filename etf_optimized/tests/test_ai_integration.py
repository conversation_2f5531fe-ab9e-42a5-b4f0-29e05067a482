#!/usr/bin/env python3
"""
测试AI配置集成功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入是否正常"""
    try:
        from etf_core.ai import multi_ai_helper, get_ai_analysis
        from etf_core.utils import config
        print("✅ 导入成功")
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_ai_providers():
    """测试AI提供商"""
    try:
        from etf_core.ai import multi_ai_helper
        available_providers = multi_ai_helper.get_available_providers()
        print(f"✅ 可用AI提供商: {available_providers}")
        return True
    except Exception as e:
        print(f"❌ 获取AI提供商失败: {e}")
        return False

def test_config():
    """测试配置"""
    try:
        from etf_bot_project.config import config
        print(f"✅ 当前AI提供商: {config.AI_PROVIDER}")
        print(f"✅ 当前模型: {config.get_current_model()}")
        return True
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 测试AI配置集成功能")
    print("=" * 40)
    
    tests = [
        ("导入测试", test_imports),
        ("AI提供商测试", test_ai_providers),
        ("配置测试", test_config)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} 失败")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！AI配置集成成功")
    else:
        print("⚠️ 部分测试失败，请检查配置")

if __name__ == "__main__":
    main()
