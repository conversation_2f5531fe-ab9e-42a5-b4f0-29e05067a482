#!/usr/bin/env python3
"""
测试AI供应商选择功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_ai_imports():
    """测试AI模块导入"""
    try:
        from etf_core.ai import multi_ai_helper, get_ai_analysis
        from etf_core.utils import config
        print("✅ AI模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ AI模块导入失败: {e}")
        return False

def test_ai_providers():
    """测试AI提供商获取"""
    try:
        from etf_core.ai import multi_ai_helper
        available_providers = multi_ai_helper.get_available_providers()
        print(f"✅ 可用AI提供商: {available_providers}")
        
        if not available_providers:
            print("⚠️ 没有可用的AI提供商，请配置API密钥")
        
        return True
    except Exception as e:
        print(f"❌ 获取AI提供商失败: {e}")
        return False

def test_ai_config():
    """测试AI配置"""
    try:
        from etf_core.utils import config
        print(f"✅ AI配置:")
        print(f"  - 当前提供商: {getattr(config, 'AI_PROVIDER', 'openai')}")
        print(f"  - OpenAI模型: {getattr(config, 'GPT_MODEL', 'gpt-4o-mini')}")
        print(f"  - 千问模型: {getattr(config, 'QWEN_MODEL', 'qwen3-30b-a3b')}")
        print(f"  - 火山云模型: {getattr(config, 'VOLCENGINE_MODEL', 'doubao-lite-4k')}")
        print(f"  - 温度参数: {getattr(config, 'AI_TEMPERATURE', 0.3)}")
        print(f"  - 最大令牌: {getattr(config, 'AI_MAX_TOKENS', 4000)}")
        
        # 测试AI模型配置
        ai_models = getattr(config, 'AI_MODELS', {})
        print(f"  - 支持的AI模型: {list(ai_models.keys())}")
        
        return True
    except Exception as e:
        print(f"❌ AI配置测试失败: {e}")
        return False

def test_ai_analysis():
    """测试AI分析功能"""
    try:
        from etf_core.ai import get_ai_analysis, multi_ai_helper
        
        # 获取可用提供商
        available_providers = multi_ai_helper.get_available_providers()
        
        if not available_providers:
            print("⚠️ 没有可用的AI提供商，跳过分析测试")
            return True
        
        # 测试每个可用的提供商
        test_prompt = "请简单说一句话测试AI连接"
        
        for provider_key, provider_name in available_providers.items():
            print(f"🧪 测试 {provider_name} ({provider_key})...")
            try:
                result = get_ai_analysis(test_prompt, provider=provider_key)
                if result and "暂时不可用" not in result:
                    print(f"  ✅ {provider_name} 连接正常")
                    print(f"  📝 回复: {result[:100]}...")
                else:
                    print(f"  ❌ {provider_name} 连接失败")
            except Exception as e:
                print(f"  ❌ {provider_name} 测试异常: {e}")
        
        return True
    except Exception as e:
        print(f"❌ AI分析测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 测试ETF项目AI供应商选择功能")
    print("=" * 50)
    
    tests = [
        ("AI模块导入", test_ai_imports),
        ("AI提供商获取", test_ai_providers),
        ("AI配置", test_ai_config),
        ("AI分析功能", test_ai_analysis)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} 失败")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！AI供应商选择功能正常")
    else:
        print("⚠️ 部分测试失败，请检查配置")
    
    print("\n💡 使用说明:")
    print("1. 启动投资组合管理器: streamlit run etf_web/portfolio_manager.py")
    print("2. 在'实时分析'页面可以看到AI智能分析配置区域")
    print("3. 选择不同的AI提供商和模型进行分析")
    print("4. 如果没有可用的AI提供商，请前往'AI配置'页面配置API密钥")

if __name__ == "__main__":
    main()
