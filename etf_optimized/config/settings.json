{"ai_provider": "openai", "openai_base_url": "https://api.openai.com/v1", "qwen_model": "qwen-turbo", "volcengine_endpoint": "https://ark.cn-beijing.volces.com/api/v3/chat/completions", "volcengine_model": "doubao-lite-4k", "max_retries": 3, "retry_delay": 1.0, "cache_timeout": 300, "historical_cache_timeout": 3600, "lookback_days": 365, "ai_temperature": 0.3, "ai_max_retries": 3, "ai_max_tokens": 4000, "gpt_model": "gpt-4o-mini", "retreat_thresholds": [-5, -10, -15], "rise_thresholds": [5, 10, 15], "ai_models": {"openai": {"models": ["gpt-4o-mini", "gpt-4o", "gpt-4.1-mini"], "default_model": "gpt-4o-mini", "api_key_env": "OPENAI_API_KEY", "display_name": "OpenAI GPT"}, "qwen": {"models": ["qwen-turbo", "qwen-plus", "qwen-max", "qwen2.5-72b-instruct"], "default_model": "qwen-turbo", "api_key_env": "QWEN_API_KEY", "display_name": "千问 (<PERSON><PERSON>)"}, "volcengine": {"models": ["doubao-lite-4k", "doubao-pro-4k", "doubao-pro-32k"], "default_model": "doubao-lite-4k", "api_key_env": "VOLCENGINE_API_KEY", "display_name": "火山云 (豆包)"}}, "data_sources": {"akshare": {"enabled": true, "priority": 1, "timeout": 30}}, "exchange_mapping": {"sh_prefixes": ["50", "51", "52", "56", "58"], "sz_prefixes": ["15", "16", "17", "18", "19"]}, "price_validation": {"min_price": 0.1, "max_price": 1000.0, "required_columns": ["收盘", "开盘", "最高", "最低"]}}