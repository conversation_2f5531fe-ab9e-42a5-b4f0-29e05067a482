#!/usr/bin/env python3
"""
交互式智能爬虫

提供友好的命令行界面，支持自然语言输入
"""

import asyncio
import os
import sys
from typing import Optional
from intelligent_web_scraper import IntelligentWebScraper, ScrapingResult


class InteractiveScraper:
    """交互式爬虫界面"""
    
    def __init__(self):
        self.scraper: Optional[IntelligentWebScraper] = None
        self.setup_openai()
    
    def setup_openai(self):
        """设置 OpenAI API"""
        api_key = os.getenv('OPENAI_API_KEY')
        
        if not api_key:
            print("🔑 请设置 OpenAI API Key")
            print("方法1: 设置环境变量 OPENAI_API_KEY")
            print("方法2: 在下面直接输入")
            print()
            
            api_key = input("请输入你的 OpenAI API Key: ").strip()
            
            if not api_key:
                print("❌ 未提供 API Key，程序退出")
                sys.exit(1)
        
        try:
            self.scraper = IntelligentWebScraper(api_key)
            print("✅ OpenAI API 连接成功")
        except Exception as e:
            print(f"❌ OpenAI API 连接失败: {e}")
            sys.exit(1)
    
    def show_welcome(self):
        """显示欢迎信息"""
        print("\n" + "="*60)
        print("🤖 智能网页爬虫系统")
        print("="*60)
        print("✨ 支持自然语言输入，AI 智能解析")
        print("🔐 自动检测登录需求")
        print("📊 智能内容提取")
        print()
        print("💡 使用示例:")
        print("  • 我想爬取淘宝商品页面的价格和评论，网址是...")
        print("  • 帮我抓取这个新闻网站的标题和内容: https://...")
        print("  • 爬取京东商品信息，我的账号是xxx，密码是xxx")
        print()
        print("⌨️  输入 'quit' 或 'exit' 退出程序")
        print("⌨️  输入 'help' 查看更多帮助")
        print("="*60)
    
    def show_help(self):
        """显示帮助信息"""
        print("\n📖 帮助信息")
        print("-"*40)
        print("🎯 支持的输入格式:")
        print("  1. 基本爬取:")
        print("     '爬取这个网页的标题: https://example.com'")
        print("     '提取价格信息: https://shop.com/product'")
        print()
        print("  2. 需要登录的网站:")
        print("     '爬取会员页面内容，账号**************，密码123456'")
        print("     '登录后获取订单信息: https://site.com/orders'")
        print()
        print("  3. 具体内容要求:")
        print("     '提取商品名称、价格、库存状态'")
        print("     '获取文章标题、作者、发布时间、正文内容'")
        print()
        print("🔧 支持的内容类型:")
        print("  • 文本内容 (标题、描述、正文)")
        print("  • 价格信息")
        print("  • 图片链接")
        print("  • 表格数据")
        print("  • 列表信息")
        print("  • 联系方式")
        print()
        print("⚠️  注意事项:")
        print("  • 请遵守网站的使用条款")
        print("  • 合理控制爬取频率")
        print("  • 保护个人隐私信息")
        print("-"*40)
    
    def show_examples(self):
        """显示使用示例"""
        print("\n🌟 使用示例")
        print("-"*40)
        
        examples = [
            {
                "description": "电商产品信息",
                "input": "我想爬取这个商品页面的名称、价格、评分: https://item.taobao.com/item.htm?id=123456",
                "note": "自动提取商品相关信息"
            },
            {
                "description": "新闻文章内容", 
                "input": "帮我抓取这篇新闻的标题、作者、发布时间和正文: https://news.example.com/article/123",
                "note": "结构化提取新闻要素"
            },
            {
                "description": "需要登录的网站",
                "input": "爬取我的订单信息，网站是https://shop.com/orders，账号是**************，密码是mypassword",
                "note": "自动登录后爬取个人数据"
            },
            {
                "description": "表格数据提取",
                "input": "提取这个页面的所有表格数据: https://data.gov.cn/statistics",
                "note": "智能识别和提取表格"
            }
        ]
        
        for i, example in enumerate(examples, 1):
            print(f"{i}. {example['description']}")
            print(f"   输入: {example['input']}")
            print(f"   说明: {example['note']}")
            print()
        
        print("-"*40)
    
    async def process_input(self, user_input: str) -> bool:
        """处理用户输入"""
        user_input = user_input.strip()
        
        if not user_input:
            return True
        
        # 处理特殊命令
        if user_input.lower() in ['quit', 'exit', 'q']:
            print("👋 再见！")
            return False
        
        if user_input.lower() in ['help', 'h', '帮助']:
            self.show_help()
            return True
        
        if user_input.lower() in ['examples', 'example', '示例']:
            self.show_examples()
            return True
        
        # 执行爬取
        print(f"\n🔍 正在分析您的需求...")
        print(f"📝 输入内容: {user_input}")
        
        try:
            result = await self.scraper.scrape_from_natural_language(user_input)
            self.display_result(result)
            
            # 询问是否保存结果
            if result.success:
                save_choice = input("\n💾 是否保存结果到文件? (y/n): ").strip().lower()
                if save_choice in ['y', 'yes', '是']:
                    filename = self.scraper.save_result(result)
                    print(f"✅ 结果已保存到: {filename}")
            
        except Exception as e:
            print(f"❌ 处理过程中发生错误: {e}")
        
        return True
    
    def display_result(self, result: ScrapingResult):
        """显示爬取结果"""
        print("\n" + "="*50)
        print("📊 爬取结果")
        print("="*50)
        
        if result.success:
            print("✅ 爬取成功!")
            print(f"🔗 目标URL: {result.url}")
            
            if result.extracted_data:
                print("\n📋 提取的内容:")
                self._display_extracted_data(result.extracted_data)
            
            print(f"\n📄 原始内容长度: {len(result.raw_content)} 字符")
            
            if result.metadata:
                intent = result.metadata.get('intent', {})
                if intent.get('needs_login'):
                    print("🔐 使用了认证登录")
                
                confidence = result.extracted_data.get('confidence', 0)
                if confidence:
                    print(f"🎯 提取置信度: {confidence:.1%}")
        
        else:
            print("❌ 爬取失败")
            print(f"🔗 目标URL: {result.url}")
            print(f"💥 错误信息: {result.error_message}")
            
            # 显示可能的解决方案
            if "登录" in str(result.error_message):
                print("\n💡 可能的解决方案:")
                print("  • 检查用户名和密码是否正确")
                print("  • 确认网站是否需要验证码")
                print("  • 尝试提供完整的登录页面URL")
        
        print("="*50)
    
    def _display_extracted_data(self, data: dict, indent: int = 0):
        """递归显示提取的数据"""
        prefix = "  " * indent
        
        for key, value in data.items():
            if isinstance(value, dict):
                print(f"{prefix}📁 {key}:")
                self._display_extracted_data(value, indent + 1)
            elif isinstance(value, list):
                print(f"{prefix}📋 {key}: ({len(value)} 项)")
                for i, item in enumerate(value[:5]):  # 只显示前5项
                    print(f"{prefix}  {i+1}. {item}")
                if len(value) > 5:
                    print(f"{prefix}  ... 还有 {len(value) - 5} 项")
            else:
                # 限制显示长度
                display_value = str(value)
                if len(display_value) > 100:
                    display_value = display_value[:100] + "..."
                print(f"{prefix}📌 {key}: {display_value}")
    
    async def run(self):
        """运行交互式界面"""
        self.show_welcome()
        
        while True:
            try:
                print("\n" + "-"*50)
                user_input = input("🤖 请描述您的爬取需求: ").strip()
                
                should_continue = await self.process_input(user_input)
                if not should_continue:
                    break
                    
            except KeyboardInterrupt:
                print("\n\n👋 程序被用户中断，再见！")
                break
            except Exception as e:
                print(f"\n❌ 发生未预期的错误: {e}")
                print("请重试或输入 'help' 查看帮助")


async def main():
    """主函数"""
    scraper = InteractiveScraper()
    await scraper.run()


if __name__ == "__main__":
    asyncio.run(main())
