#!/usr/bin/env python3
"""
智能网页爬虫系统

基于自然语言输入和 OpenAI LLM 的智能爬虫，支持：
- 自然语言意图理解
- 智能登录检测
- 自动内容提取
- AI 驱动的结果解析
"""

import asyncio
import json
import logging
import re
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from dataclasses import dataclass

try:
    import openai
    from openai import OpenAI
except ImportError:
    openai = None
    OpenAI = None

from langchain_html_processor import (
    AuthenticatedHTMLLoader, 
    SmartHTMLLoader,
    LoaderConfig
)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class ScrapingIntent:
    """爬取意图数据结构"""
    url: str
    content_requirements: List[str]
    needs_login: bool
    username: Optional[str] = None
    password: Optional[str] = None
    login_url: Optional[str] = None
    additional_info: Dict[str, Any] = None


@dataclass
class ScrapingResult:
    """爬取结果数据结构"""
    success: bool
    url: str
    extracted_data: Dict[str, Any]
    raw_content: str
    metadata: Dict[str, Any]
    error_message: Optional[str] = None


class IntentAnalyzer:
    """自然语言意图分析器"""
    
    def __init__(self, openai_client: OpenAI):
        self.client = openai_client
    
    def analyze_intent(self, user_input: str) -> ScrapingIntent:
        """分析用户的自然语言输入，提取爬取意图"""
        
        system_prompt = """
你是一个智能网页爬虫助手。分析用户的自然语言输入，提取以下信息：

1. 目标URL（如果提供）
2. 需要爬取的内容类型（如：价格、标题、评论、图片等）
3. 是否需要登录
4. 登录信息（如果提供）
5. 其他相关信息

请以JSON格式返回结果：
{
    "url": "目标网址",
    "content_requirements": ["需要爬取的内容1", "内容2"],
    "needs_login": true/false,
    "username": "用户名（如果提供）",
    "password": "密码（如果提供）",
    "login_url": "登录页面URL（如果不同于主URL）",
    "additional_info": {
        "其他": "相关信息"
    }
}

如果信息不完整，在additional_info中说明需要用户补充什么信息。
"""
        
        try:
            response = self.client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_input}
                ],
                temperature=0.1
            )
            
            result_text = response.choices[0].message.content
            
            # 尝试解析JSON
            try:
                result_json = json.loads(result_text)
                return ScrapingIntent(
                    url=result_json.get('url', ''),
                    content_requirements=result_json.get('content_requirements', []),
                    needs_login=result_json.get('needs_login', False),
                    username=result_json.get('username'),
                    password=result_json.get('password'),
                    login_url=result_json.get('login_url'),
                    additional_info=result_json.get('additional_info', {})
                )
            except json.JSONDecodeError:
                logger.error(f"Failed to parse AI response as JSON: {result_text}")
                return ScrapingIntent(
                    url='',
                    content_requirements=[],
                    needs_login=False,
                    additional_info={'error': 'AI响应解析失败', 'raw_response': result_text}
                )
                
        except Exception as e:
            logger.error(f"Intent analysis failed: {e}")
            return ScrapingIntent(
                url='',
                content_requirements=[],
                needs_login=False,
                additional_info={'error': f'意图分析失败: {str(e)}'}
            )


class ContentExtractor:
    """智能内容提取器"""
    
    def __init__(self, openai_client: OpenAI):
        self.client = openai_client
    
    def extract_content(self, html_content: str, requirements: List[str], url: str) -> Dict[str, Any]:
        """基于需求从HTML内容中智能提取信息"""
        
        # 限制内容长度以避免token限制
        content_preview = html_content[:8000] if len(html_content) > 8000 else html_content
        
        system_prompt = f"""
你是一个智能内容提取器。从提供的HTML内容中提取用户需要的信息。

用户需要提取的内容：{', '.join(requirements)}
网页URL：{url}

请分析HTML内容，提取相关信息，并以JSON格式返回：
{{
    "extracted_data": {{
        "内容类型1": ["提取的数据1", "数据2"],
        "内容类型2": "单个数据",
        "其他": "相关信息"
    }},
    "summary": "提取结果的简要总结",
    "confidence": 0.8,
    "notes": "提取过程中的注意事项"
}}

注意：
1. 尽量提取结构化数据
2. 如果是价格，保留货币符号
3. 如果是列表，返回数组
4. 如果找不到相关内容，说明原因
"""
        
        try:
            response = self.client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"HTML内容：\n{content_preview}"}
                ],
                temperature=0.1
            )
            
            result_text = response.choices[0].message.content
            
            try:
                return json.loads(result_text)
            except json.JSONDecodeError:
                logger.error(f"Failed to parse extraction result: {result_text}")
                return {
                    "extracted_data": {"raw_ai_response": result_text},
                    "summary": "AI响应解析失败",
                    "confidence": 0.0,
                    "notes": "无法解析AI返回的JSON格式"
                }
                
        except Exception as e:
            logger.error(f"Content extraction failed: {e}")
            return {
                "extracted_data": {},
                "summary": f"内容提取失败: {str(e)}",
                "confidence": 0.0,
                "notes": "AI内容提取过程中发生错误"
            }


class IntelligentWebScraper:
    """智能网页爬虫主类"""
    
    def __init__(self, openai_api_key: str):
        if not openai or not OpenAI:
            raise ImportError("请安装 openai 库: pip install openai")
        
        self.openai_client = OpenAI(api_key=openai_api_key)
        self.intent_analyzer = IntentAnalyzer(self.openai_client)
        self.content_extractor = ContentExtractor(self.openai_client)
        
        # 配置爬虫
        self.loader_config = LoaderConfig(
            timeout=60,
            wait_for_js=3.0,
            retries=2,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        )
    
    async def scrape_from_natural_language(self, user_input: str) -> ScrapingResult:
        """基于自然语言输入进行智能爬取"""
        
        logger.info(f"分析用户输入: {user_input}")
        
        # 1. 分析用户意图
        intent = self.intent_analyzer.analyze_intent(user_input)
        
        if not intent.url:
            return ScrapingResult(
                success=False,
                url='',
                extracted_data={},
                raw_content='',
                metadata={'intent': intent.__dict__},
                error_message="无法从输入中提取有效的URL"
            )
        
        logger.info(f"提取的意图: URL={intent.url}, 需要登录={intent.needs_login}")
        
        # 2. 选择合适的加载器
        if intent.needs_login and intent.username and intent.password:
            loader = AuthenticatedHTMLLoader(self.loader_config)
            login_url = intent.login_url or intent.url
            
            try:
                documents = await loader.load_with_auth(
                    target_url=intent.url,
                    username=intent.username,
                    password=intent.password,
                    login_url=login_url
                )
            except Exception as e:
                return ScrapingResult(
                    success=False,
                    url=intent.url,
                    extracted_data={},
                    raw_content='',
                    metadata={'intent': intent.__dict__},
                    error_message=f"认证爬取失败: {str(e)}"
                )
        else:
            loader = SmartHTMLLoader(self.loader_config)
            try:
                documents = await asyncio.get_event_loop().run_in_executor(
                    None, loader.load, intent.url
                )
            except Exception as e:
                return ScrapingResult(
                    success=False,
                    url=intent.url,
                    extracted_data={},
                    raw_content='',
                    metadata={'intent': intent.__dict__},
                    error_message=f"普通爬取失败: {str(e)}"
                )
        
        if not documents:
            return ScrapingResult(
                success=False,
                url=intent.url,
                extracted_data={},
                raw_content='',
                metadata={'intent': intent.__dict__},
                error_message="未能获取网页内容"
            )
        
        document = documents[0]
        raw_content = document.page_content
        
        logger.info(f"成功获取内容，长度: {len(raw_content)} 字符")
        
        # 3. 智能内容提取
        if intent.content_requirements:
            extracted_data = self.content_extractor.extract_content(
                raw_content, 
                intent.content_requirements, 
                intent.url
            )
        else:
            extracted_data = {
                "raw_content": raw_content[:2000],
                "summary": "未指定具体提取要求，返回原始内容预览",
                "confidence": 1.0
            }
        
        return ScrapingResult(
            success=True,
            url=intent.url,
            extracted_data=extracted_data,
            raw_content=raw_content,
            metadata={
                'intent': intent.__dict__,
                'document_metadata': document.metadata,
                'timestamp': datetime.now().isoformat()
            }
        )
    
    def save_result(self, result: ScrapingResult, filename: Optional[str] = None) -> str:
        """保存爬取结果到文件"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"scraping_result_{timestamp}.json"
        
        result_dict = {
            "success": result.success,
            "url": result.url,
            "extracted_data": result.extracted_data,
            "raw_content_preview": result.raw_content[:1000] if result.raw_content else "",
            "raw_content_length": len(result.raw_content) if result.raw_content else 0,
            "metadata": result.metadata,
            "error_message": result.error_message
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(result_dict, f, indent=2, ensure_ascii=False)
        
        logger.info(f"结果已保存到: {filename}")
        return filename
