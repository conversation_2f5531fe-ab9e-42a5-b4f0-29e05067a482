### 📦 Python 虚拟环境设置指南（`virtualenv-setup.md`）

本项目推荐使用 Python 虚拟环境来隔离依赖，避免与全局环境产生冲突。

---

## ✅ 1. 创建虚拟环境

在项目根目录下执行以下命令（仅需一次）：

```bash
python3 -m venv venv
```

这将在当前目录下创建一个名为 `venv/` 的虚拟环境文件夹。

---

## ✅ 2. 激活虚拟环境

激活方式依操作系统与终端类型而异：

### macOS / Linux / WSL / Git Bash：

```bash
source venv/bin/activate
```

### Windows（CMD）：

```cmd
venv\Scripts\activate.bat
```

### Windows（PowerShell）：

```powershell
venv\Scripts\Activate.ps1
```

激活成功后，终端提示符前会显示 `(venv)`。

---

## ✅ 3. 安装依赖

如果项目提供了 `requirements.txt`：

```bash
pip install -r requirements.txt
```

或者你可以手动安装依赖：

```bash
pip install -U pip
pip install fastapi uvicorn playwright python-dotenv
python -m playwright install chromium
```

---

## ✅ 4. 运行项目

激活虚拟环境后即可运行项目，例如：

```bash
python start_agent_server.py
```

---

## ✅ 5. 后续使用

每次打开终端进入项目时，请重新激活虚拟环境：

```bash
source venv/bin/activate
```

退出虚拟环境使用：

```bash
deactivate
```

---

## ✅ 6. 忽略不应提交的文件

确保 `.gitignore` 文件中包含以下内容：

```
```
---

## ✅ 7. 生成依赖文件（可选）

如果你在虚拟环境中安装了新库，建议更新 `requirements.txt`：

```bash
pip freeze > requirements.txt
```
