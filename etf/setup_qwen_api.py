#!/usr/bin/env python3
"""
千问API配置助手
帮助用户正确配置千问API密钥和模型
"""
import os
import sys

def main():
    """千问API配置主函数"""
    print("🤖 千问API配置助手")
    print("=" * 50)
    
    # 检查当前配置
    current_api_key = os.getenv('QWEN_API_KEY', '')
    current_model = os.getenv('QWEN_MODEL', 'qwen-turbo')
    
    print("📋 当前配置:")
    if current_api_key:
        print(f"✅ QWEN_API_KEY: {current_api_key[:10]}...{current_api_key[-5:]}")
    else:
        print("❌ QWEN_API_KEY: 未设置")
    print(f"📦 QWEN_MODEL: {current_model}")
    
    print("\n" + "=" * 50)
    print("🔧 千问API配置指南:")
    print()
    print("1️⃣ 获取API密钥:")
    print("   • 访问: https://dashscope.console.aliyun.com/")
    print("   • 登录阿里云账号")
    print("   • 在控制台中创建API密钥")
    print("   • 复制完整的API密钥 (以sk-开头，约51个字符)")
    print()
    print("2️⃣ 设置环境变量:")
    print("   方法一 - 临时设置 (当前终端有效):")
    print("   export QWEN_API_KEY='your-api-key-here'")
    print()
    print("   方法二 - 永久设置 (推荐):")
    print("   echo 'export QWEN_API_KEY=\"your-api-key-here\"' >> ~/.bashrc")
    print("   source ~/.bashrc")
    print()
    print("   方法三 - 使用.env文件:")
    print("   在项目根目录创建.env文件，添加:")
    print("   QWEN_API_KEY=your-api-key-here")
    print()
    print("3️⃣ 可用模型:")
    print("   • qwen-turbo (推荐，速度快，成本低)")
    print("   • qwen-plus (平衡性能和成本)")
    print("   • qwen-max (最强性能，成本较高)")
    print("   • qwen-long (长文本处理)")
    print()
    print("4️⃣ 设置模型 (可选):")
    print("   export QWEN_MODEL='qwen-turbo'")
    print()
    
    # 交互式配置
    print("=" * 50)
    print("🛠️ 交互式配置 (可选)")
    
    try:
        choice = input("\n是否要现在配置API密钥? (y/n): ").lower().strip()
        if choice == 'y':
            api_key = input("请输入您的千问API密钥: ").strip()
            if api_key and api_key.startswith('sk-') and len(api_key) > 40:
                # 设置环境变量 (仅当前会话有效)
                os.environ['QWEN_API_KEY'] = api_key
                print("✅ API密钥已设置 (当前会话有效)")
                
                # 选择模型
                print("\n可用模型:")
                models = ['qwen-turbo', 'qwen-plus', 'qwen-max', 'qwen-long']
                for i, model in enumerate(models, 1):
                    print(f"  {i}. {model}")
                
                model_choice = input(f"选择模型 (1-{len(models)}, 默认1): ").strip()
                if model_choice.isdigit() and 1 <= int(model_choice) <= len(models):
                    selected_model = models[int(model_choice) - 1]
                else:
                    selected_model = 'qwen-turbo'
                
                os.environ['QWEN_MODEL'] = selected_model
                print(f"✅ 模型已设置为: {selected_model}")
                
                # 测试配置
                print("\n🧪 测试API连接...")
                test_api()
                
            else:
                print("❌ API密钥格式不正确，请确保以sk-开头且长度足够")
        else:
            print("⏭️ 跳过交互式配置")
            
    except KeyboardInterrupt:
        print("\n\n⏹️ 配置已取消")
        return
    
    print("\n" + "=" * 50)
    print("💡 配置完成后，重启应用以使配置生效")
    print("🚀 运行: python start_portfolio_manager.py")

def test_api():
    """测试千问API连接"""
    try:
        import sys
        sys.path.append('.')
        from etf_bot_project.multi_ai_helper import MultiAIHelper
        
        # 重新初始化客户端以使用新的API密钥
        helper = MultiAIHelper(provider='qwen')
        result = helper.get_analysis("你好，请简单回复'测试成功'")
        
        if result and "测试成功" in result:
            print("✅ 千问API连接测试成功!")
        elif result:
            print(f"✅ 千问API连接成功，回复: {result[:50]}...")
        else:
            print("❌ 千问API连接测试失败")
            
    except Exception as e:
        print(f"❌ 千问API测试失败: {e}")

if __name__ == "__main__":
    main()
