#!/usr/bin/env python3
"""
ETF投资组合管理器启动脚本
"""
import os
import sys
import subprocess

def main():
    """启动投资组合管理器"""
    print("🚀 启动ETF投资组合管理器...")
    print("=" * 50)

    # 检查依赖
    try:
        import streamlit
        print("✅ Streamlit 已安装")
    except ImportError:
        print("❌ Streamlit 未安装，正在安装...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "streamlit"])
        print("✅ Streamlit 安装完成")

    # 检查AI模型依赖
    ai_deps = [
        ("openai", "OpenAI"),
        ("dashscope", "千问"),
        ("volcengine", "火山云")
    ]

    installed_ai_deps = []
    missing_ai_deps = []

    for module, name in ai_deps:
        try:
            __import__(module)
            installed_ai_deps.append(name)
        except ImportError:
            missing_ai_deps.append(name)

    # 显示安装状态
    if installed_ai_deps:
        print(f"✅ 已安装AI SDK: {', '.join(installed_ai_deps)}")

    if missing_ai_deps:
        print(f"⚠️ 未安装AI SDK: {', '.join(missing_ai_deps)} - 相关AI功能将不可用")

    # 检查AI配置
    print("\n🤖 AI模型配置检查:")
    from etf_bot_project.multi_ai_helper import multi_ai_helper
    available_providers = multi_ai_helper.get_available_providers()

    if available_providers:
        print(f"✅ 可用AI模型: {', '.join(available_providers.values())}")
    else:
        print("⚠️ 没有可用的AI模型，请配置API密钥")
        print("💡 运行 'python setup_ai_config.py' 进行配置")
    
    # 获取当前目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    manager_file = os.path.join(current_dir, "etf_portfolio_manager.py")
    
    # 启动Streamlit应用
    print(f"📂 工作目录: {current_dir}")
    print(f"📄 管理器文件: {manager_file}")
    print("\n🌐 正在启动Web界面...")
    print("💡 提示：浏览器将自动打开管理界面")
    print("🔗 如果浏览器未自动打开，请访问: http://localhost:8501")
    print("🔧 AI模型配置已集成到主界面，可在导航菜单中找到")
    print("\n⏹️  按 Ctrl+C 停止服务")
    print("=" * 50)
    
    try:
        # 启动streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            manager_file,
            "--server.port", "8501",
            "--server.address", "localhost",
            "--browser.gatherUsageStats", "false"
        ])
    except KeyboardInterrupt:
        print("\n\n⏹️  ETF投资组合管理器已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("💡 请检查Python环境和依赖包是否正确安装")

if __name__ == "__main__":
    main()
