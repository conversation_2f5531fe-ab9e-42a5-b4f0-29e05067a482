#!/usr/bin/env python3
"""
Restaurant Depot Content Scraper

This script uses the langchain_html_processor with authentication support
to scrape product information from Restaurant Depot's member portal.

Usage:
    python restaurant_depot_scraper.py

Requirements:
    - langchain_html_processor with AuthenticatedHTMLLoader
    - playwright (install with: playwright install chromium)
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional
from datetime import datetime
from pathlib import Path

from langchain_html_processor import AuthenticatedHTMLLoader, LoaderConfig
from bs4 import BeautifulSoup


class RestaurantDepotScraper:
    """Specialized scraper for Restaurant Depot member portal"""
    
    def __init__(self, username: str, password: str):
        self.username = username
        self.password = password
        self.login_url = "https://member.restaurantdepot.com/login"
        self.base_url = "https://member.restaurantdepot.com"
        
        # Configure loader for Restaurant Depot
        self.config = LoaderConfig(
            timeout=60,
            wait_for_js=3.0,
            retries=3,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        )
        
        self.loader = AuthenticatedHTMLLoader(self.config)
        
        # Restaurant Depot will use specialized login method automatically
        self.custom_selectors = None  # Not needed for Restaurant Depot
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    async def scrape_products(self, category_url: str) -> Dict:
        """Scrape products from a specific category URL"""
        self.logger.info(f"Starting to scrape products from: {category_url}")
        
        try:
            # Load the page with authentication
            documents = await self.loader.load_with_auth(
                target_url=category_url,
                username=self.username,
                password=self.password,
                login_url=self.login_url,
                custom_selectors=self.custom_selectors
            )
            
            if not documents:
                self.logger.error("No documents returned from authenticated loading")
                return {"error": "Failed to load content", "products": []}
            
            document = documents[0]
            self.logger.info(f"Successfully loaded content: {len(document.page_content)} characters")
            
            # Parse the HTML content to extract product information
            products = self._extract_products(document.page_content, category_url)
            
            result = {
                "timestamp": datetime.now().isoformat(),
                "source_url": category_url,
                "login_url": self.login_url,
                "total_products": len(products),
                "products": products,
                "metadata": document.metadata
            }
            
            self.logger.info(f"Extracted {len(products)} products")
            return result
            
        except Exception as e:
            self.logger.error(f"Error scraping products: {e}")
            return {"error": str(e), "products": []}
    
    def _extract_products(self, html_content: str, source_url: str) -> List[Dict]:
        """Extract product information from HTML content"""
        soup = BeautifulSoup(html_content, 'html.parser')
        products = []

        # Try Restaurant Depot specific parsing first
        rd_products = self._extract_restaurant_depot_products(html_content)
        if rd_products:
            return rd_products

        # Common product container selectors to try
        product_selectors = [
            '.product-item',
            '.product-card',
            '.product',
            '[data-product]',
            '.item',
            '.product-listing-item',
            '.product-tile'
        ]

        product_elements = []
        for selector in product_selectors:
            elements = soup.select(selector)
            if elements:
                product_elements = elements
                self.logger.info(f"Found {len(elements)} products using selector: {selector}")
                break

        if not product_elements:
            # Fallback: look for any elements that might contain product info
            self.logger.warning("No products found with standard selectors, trying fallback")
            # Look for elements with price indicators
            price_elements = soup.find_all(string=lambda text: text and '$' in str(text))
            self.logger.info(f"Found {len(price_elements)} elements with price indicators")

            # Extract all text content for manual review
            all_text = soup.get_text(separator='\n', strip=True)
            return [{
                "raw_content": all_text[:5000],  # First 5000 chars
                "note": "Could not parse structured product data, returning raw content",
                "source_url": source_url
            }]

        # Extract product information
        for i, element in enumerate(product_elements):
            try:
                product = self._extract_single_product(element, i)
                if product:
                    products.append(product)
            except Exception as e:
                self.logger.warning(f"Error extracting product {i}: {e}")
                continue

        return products

    def _extract_restaurant_depot_products(self, html_content: str) -> List[Dict]:
        """Extract products specifically from Restaurant Depot HTML"""
        import re

        products = []

        # Pattern to match Restaurant Depot product blocks
        # Looking for patterns like: "Product Name Item: 12345 UPC: 123456 BIN: 123 $XX.XX"
        pattern = r'([A-Za-z][^$\n]*?)\s+Item:\s*(\d+)\s+UPC:\s*(\d+)\s+BIN:\s*(\d+)\s+\$(\d+\.\d+)'

        matches = re.findall(pattern, html_content)

        if matches:
            self.logger.info(f"Found {len(matches)} products using Restaurant Depot pattern")

            for i, match in enumerate(matches):
                product_name, item_id, upc, bin_id, price = match

                # Clean up product name
                product_name = re.sub(r'\s+', ' ', product_name.strip())

                product = {
                    "index": i,
                    "name": product_name,
                    "item_id": item_id,
                    "upc": upc,
                    "bin": bin_id,
                    "price": f"${price}",
                    "source": "restaurant_depot_pattern"
                }

                products.append(product)

        # If no matches with the first pattern, try a simpler one
        if not products:
            # Look for price patterns and try to extract surrounding text
            price_pattern = r'([^$\n]{10,100})\$(\d+\.\d+)'
            price_matches = re.findall(price_pattern, html_content)

            if price_matches:
                self.logger.info(f"Found {len(price_matches)} products using price pattern")

                for i, (text_before, price) in enumerate(price_matches[:20]):  # Limit to 20
                    # Clean up the text before price
                    product_text = re.sub(r'\s+', ' ', text_before.strip())

                    # Try to extract product name (usually the last meaningful part)
                    words = product_text.split()
                    if len(words) > 2:
                        # Take the last few words as product name
                        product_name = ' '.join(words[-5:]) if len(words) >= 5 else product_text
                    else:
                        product_name = product_text

                    if len(product_name) > 5:  # Only include if we have a reasonable name
                        product = {
                            "index": i,
                            "name": product_name,
                            "price": f"${price}",
                            "raw_text": text_before,
                            "source": "price_pattern"
                        }
                        products.append(product)

        return products
    
    def _extract_single_product(self, element, index: int) -> Optional[Dict]:
        """Extract information from a single product element"""
        product = {"index": index}
        
        # Extract product name/title
        title_selectors = [
            '.product-name', '.product-title', '.title', 'h1', 'h2', 'h3', 'h4',
            '.name', '[data-product-name]', '.product-description'
        ]
        
        for selector in title_selectors:
            title_elem = element.select_one(selector)
            if title_elem:
                product['name'] = title_elem.get_text(strip=True)
                break
        
        # Extract price
        price_selectors = [
            '.price', '.product-price', '.cost', '[data-price]', '.amount'
        ]
        
        for selector in price_selectors:
            price_elem = element.select_one(selector)
            if price_elem:
                price_text = price_elem.get_text(strip=True)
                product['price'] = price_text
                break
        
        # Extract product ID/SKU
        id_selectors = [
            '[data-product-id]', '[data-sku]', '.sku', '.product-id', '.item-number'
        ]
        
        for selector in id_selectors:
            id_elem = element.select_one(selector)
            if id_elem:
                if id_elem.get('data-product-id'):
                    product['product_id'] = id_elem.get('data-product-id')
                elif id_elem.get('data-sku'):
                    product['sku'] = id_elem.get('data-sku')
                else:
                    product['id'] = id_elem.get_text(strip=True)
                break
        
        # Extract image URL
        img_elem = element.select_one('img')
        if img_elem:
            img_src = img_elem.get('src') or img_elem.get('data-src')
            if img_src:
                product['image_url'] = img_src
        
        # Extract product URL/link
        link_elem = element.select_one('a')
        if link_elem:
            href = link_elem.get('href')
            if href:
                if href.startswith('/'):
                    product['product_url'] = self.base_url + href
                else:
                    product['product_url'] = href
        
        # Extract any additional text content
        product['raw_text'] = element.get_text(separator=' ', strip=True)
        
        # Only return if we found at least a name or some meaningful content
        if product.get('name') or len(product.get('raw_text', '')) > 10:
            return product
        
        return None
    
    def save_results(self, results: Dict, filename: Optional[str] = None):
        """Save scraping results to JSON file"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"restaurant_depot_products_{timestamp}.json"
        
        filepath = Path(filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Results saved to: {filepath.absolute()}")
            print(f"✅ Results saved to: {filepath.absolute()}")
            
        except Exception as e:
            self.logger.error(f"Error saving results: {e}")
            print(f"❌ Error saving results: {e}")


async def main():
    """Main function to run the scraper"""
    # Restaurant Depot credentials
    USERNAME = "<EMAIL>"
    PASSWORD = "Jetsons823"
    
    # Target URL with multiple categories
    TARGET_URL = "https://member.restaurantdepot.com/products?sort=saleranking&it=product&category=1%7CBeverages%20%26%20Mixers,1%7CCandy%20%26%20Snacks,1%7CDry%20Groceries,1%7CJanitorial%20Supplies,1%7CRetail%20Groceries%20(Food),1%7CRetail%20Groceries%20(Non-Food)"
    
    print("🚀 Starting Restaurant Depot scraper...")
    print(f"Target URL: {TARGET_URL}")
    print(f"Username: {USERNAME}")
    
    # Create scraper instance
    scraper = RestaurantDepotScraper(USERNAME, PASSWORD)
    
    # Scrape products
    results = await scraper.scrape_products(TARGET_URL)
    
    # Display results summary
    if "error" in results:
        print(f"❌ Scraping failed: {results['error']}")
    else:
        print(f"✅ Scraping completed successfully!")
        print(f"📊 Total products found: {results['total_products']}")
        
        # Show first few products as preview
        if results['products']:
            print("\n📋 Sample products:")
            for i, product in enumerate(results['products'][:3]):
                print(f"  {i+1}. {product.get('name', 'No name')} - {product.get('price', 'No price')}")
            
            if len(results['products']) > 3:
                print(f"  ... and {len(results['products']) - 3} more products")
    
    # Save results
    scraper.save_results(results)
    
    return results


if __name__ == "__main__":
    # Run the scraper
    results = asyncio.run(main())
