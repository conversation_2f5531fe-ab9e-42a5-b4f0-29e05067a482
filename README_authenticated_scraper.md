# Restaurant Depot Authenticated Scraper

这个项目扩展了 `langchain_html_processor` 来支持需要登录的网站内容抓取，特别是针对 Restaurant Depot 会员门户的产品信息抓取。

## 🚀 新功能

### AuthenticatedHTMLLoader
- 支持自动登录功能
- 基于 Playwright 的动态内容渲染
- 智能表单识别和填写
- Session 管理和错误处理
- 可配置的登录选择器

### Restaurant Depot 专用抓取器
- 自动登录 Restaurant Depot 会员系统
- 提取产品信息（名称、价格、图片等）
- 支持多分类产品抓取
- 结构化数据输出（JSON格式）

## 📋 系统要求

```bash
# Python 依赖
pip install -r requirements.txt

# Playwright 浏览器
playwright install chromium
```

## 🎯 快速开始

### 1. 测试认证功能

首先运行测试脚本确保认证功能正常：

```bash
python test_authenticated_loader.py
```

### 2. 运行 Restaurant Depot 抓取器

```bash
python restaurant_depot_scraper.py
```

### 3. 自定义使用

```python
import asyncio
from langchain_html_processor import AuthenticatedHTMLLoader, LoaderConfig

async def custom_scraping():
    # 配置加载器
    config = LoaderConfig(
        timeout=60,
        wait_for_js=3.0,
        retries=3
    )
    
    loader = AuthenticatedHTMLLoader(config)
    
    # 自定义登录选择器（可选）
    custom_selectors = {
        'username_field': ['input[name="email"]', '#email'],
        'password_field': ['input[name="password"]', '#password'],
        'login_button': ['button[type="submit"]', '.login-btn'],
        'login_form': ['form']
    }
    
    # 加载需要认证的内容
    documents = await loader.load_with_auth(
        target_url="https://example.com/protected-page",
        username="your-username",
        password="your-password",
        login_url="https://example.com/login",
        custom_selectors=custom_selectors  # 可选
    )
    
    if documents:
        content = documents[0].page_content
        print(f"成功获取内容: {len(content)} 字符")
    
# 运行
asyncio.run(custom_scraping())
```

## 🔧 配置选项

### LoaderConfig 参数

```python
config = LoaderConfig(
    timeout=60,              # 页面加载超时时间（秒）
    wait_for_js=3.0,        # JavaScript 渲染等待时间（秒）
    retries=3,              # 重试次数
    user_agent="...",       # 自定义 User-Agent
    headers={...},          # 自定义请求头
    requests_per_second=2.0 # 请求频率限制
)
```

### 自定义登录选择器

```python
custom_selectors = {
    'username_field': [      # 用户名输入框选择器（按优先级）
        'input[name="email"]',
        'input[name="username"]',
        '#email',
        '#username'
    ],
    'password_field': [      # 密码输入框选择器
        'input[name="password"]',
        'input[type="password"]',
        '#password'
    ],
    'login_button': [        # 登录按钮选择器
        'button[type="submit"]',
        'input[type="submit"]',
        '.login-button',
        '#login-btn'
    ],
    'login_form': [          # 登录表单选择器
        'form',
        '.login-form',
        '#login-form'
    ]
}
```

## 📊 输出格式

Restaurant Depot 抓取器输出 JSON 格式的结构化数据：

```json
{
  "timestamp": "2024-01-20T10:30:00",
  "source_url": "https://member.restaurantdepot.com/products...",
  "login_url": "https://member.restaurantdepot.com/login",
  "total_products": 150,
  "products": [
    {
      "index": 0,
      "name": "Product Name",
      "price": "$12.99",
      "product_id": "12345",
      "image_url": "https://...",
      "product_url": "https://...",
      "raw_text": "Complete product text..."
    }
  ],
  "metadata": {
    "title": "Page Title",
    "authenticated": true,
    "loader_type": "authenticated"
  }
}
```

## 🛠️ 故障排除

### 常见问题

1. **Playwright 未安装**
   ```bash
   playwright install chromium
   ```

2. **登录失败**
   - 检查用户名和密码
   - 验证登录 URL 是否正确
   - 检查网站是否有验证码或其他安全措施

3. **内容抓取失败**
   - 增加 `wait_for_js` 时间
   - 检查目标 URL 是否需要特殊权限
   - 验证登录状态是否保持

4. **选择器不匹配**
   - 使用浏览器开发者工具检查页面结构
   - 自定义 `custom_selectors` 参数
   - 查看日志输出的详细错误信息

### 调试模式

启用详细日志输出：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 🔒 安全注意事项

1. **凭据安全**
   - 不要在代码中硬编码密码
   - 使用环境变量或配置文件
   - 定期更换密码

2. **请求频率**
   - 遵守网站的 robots.txt
   - 设置合理的请求间隔
   - 避免对服务器造成过大负载

3. **法律合规**
   - 确保有权限访问目标网站
   - 遵守网站的使用条款
   - 尊重版权和数据保护法规

## 📈 扩展功能

### 支持其他网站

要为其他需要登录的网站创建抓取器：

1. 继承 `AuthenticatedHTMLLoader`
2. 自定义登录选择器
3. 实现特定的内容解析逻辑

```python
class CustomSiteScraper:
    def __init__(self, username, password):
        self.loader = AuthenticatedHTMLLoader()
        self.custom_selectors = {
            # 网站特定的选择器
        }
    
    async def scrape(self, url):
        documents = await self.loader.load_with_auth(
            target_url=url,
            username=self.username,
            password=self.password,
            login_url="https://site.com/login",
            custom_selectors=self.custom_selectors
        )
        return self.parse_content(documents[0].page_content)
```

## 📞 支持

如果遇到问题或需要帮助：

1. 检查日志输出中的错误信息
2. 验证网站结构是否发生变化
3. 确认登录凭据和权限
4. 查看 Playwright 和相关依赖的版本兼容性

## 🎉 成功案例

使用此工具成功抓取的网站类型：
- 电商会员系统
- 企业内部门户
- 需要登录的数据平台
- 会员制信息网站

记住：始终遵守网站的使用条款和相关法律法规！
