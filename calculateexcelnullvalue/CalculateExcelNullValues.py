import pandas as pd
import numpy as np

# 读取Excel文件
# 请将'your_file.xlsx'替换为你的实际文件名
df = pd.read_csv('/Users/<USER>/Downloads/plytix_export_data/0907___1426/plytix_export_09_07_all_data.csv')

# 初始化结果字典
results = {}

# 遍历每一列
for column in df.columns:
  # 计算非空值和空值的数量
  non_null_count = df[column].count()
  null_count = df[column].isnull().sum()

  # 计算非空值中active和非active的数量
  if 'Online Status' in df.columns:
    active_count = df[df['Online Status'] == 'active'][column].count()
    inactive_count = non_null_count - active_count
  else:
    active_count = np.nan
    inactive_count = np.nan

  # 将结果存储在字典中
  results[column] = {
    'Non-null count': non_null_count,
    'Null count': null_count,
    'Active count': active_count,
    'Inactive count': inactive_count
  }

# 将结果转换为DataFrame
result_df = pd.DataFrame(results).T

# 打印结果
print(result_df)

# 将结果保存到Excel文件
result_df.to_csv('/Users/<USER>/Downloads/plytix_export_data/0907___1426/column_statistics.csv')