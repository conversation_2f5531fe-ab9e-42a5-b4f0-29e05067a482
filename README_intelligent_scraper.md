# 🤖 智能网页爬虫系统

基于自然语言输入和 OpenAI LLM 的智能爬虫系统，支持自动登录、智能内容提取和结果分析。

## ✨ 核心特性

### 🗣️ 自然语言输入
- 用自然语言描述爬取需求
- AI 自动理解用户意图
- 智能提取 URL、登录信息、内容要求

### 🔐 智能登录检测
- 自动判断是否需要登录
- 支持多种网站的登录机制
- 基于现有的 `AuthenticatedHTMLLoader`

### 🧠 AI 驱动的内容提取
- 根据用户需求智能提取内容
- 结构化数据输出
- 支持多种内容类型（文本、价格、图片、表格等）

### 🎯 通用性强
- 不限于特定网站
- 自适应不同网页结构
- 可配置的爬取策略

## 🚀 快速开始

### 安装依赖

```bash
pip install openai langchain beautifulsoup4 playwright
python -m playwright install chromium
```

### 设置 API Key

```bash
# 方法1: 环境变量
export OPENAI_API_KEY="your-api-key-here"

# 方法2: 程序中输入
# 运行时会提示输入
```

### 交互式使用

```bash
python interactive_scraper.py
```

### 编程使用

```python
import asyncio
from intelligent_web_scraper import IntelligentWebScraper

async def example():
    scraper = IntelligentWebScraper("your-openai-api-key")
    
    # 自然语言输入
    user_input = "我想爬取淘宝商品页面的价格和评论，网址是https://item.taobao.com/item.htm?id=123456"
    
    result = await scraper.scrape_from_natural_language(user_input)
    
    if result.success:
        print("提取的数据:", result.extracted_data)
        scraper.save_result(result)
    else:
        print("错误:", result.error_message)

asyncio.run(example())
```

## 📖 使用示例

### 1. 基本网页内容提取

```
输入: "爬取这个新闻页面的标题和正文: https://news.example.com/article/123"

AI 理解:
- URL: https://news.example.com/article/123
- 内容要求: ["标题", "正文"]
- 需要登录: False

输出:
{
  "extracted_data": {
    "标题": "新闻标题内容",
    "正文": "新闻正文内容...",
    "summary": "成功提取新闻标题和正文",
    "confidence": 0.9
  }
}
```

### 2. 电商产品信息

```
输入: "提取这个商品的名称、价格、库存状态: https://shop.com/product/456"

输出:
{
  "extracted_data": {
    "商品名称": "iPhone 15 Pro",
    "价格": "$999.00",
    "库存状态": "有货",
    "其他信息": {
      "评分": "4.8/5",
      "评论数": "1,234"
    }
  }
}
```

### 3. 需要登录的网站

```
输入: "爬取我的订单信息，网站是https://shop.com/orders，账号是**************，密码是mypassword"

AI 理解:
- URL: https://shop.com/orders
- 需要登录: True
- 用户名: <EMAIL>
- 密码: mypassword
- 内容要求: ["订单信息"]

系统会自动:
1. 检测登录页面
2. 执行登录
3. 访问目标页面
4. 提取订单信息
```

### 4. 表格数据提取

```
输入: "提取这个页面的所有表格数据: https://data.gov.cn/statistics"

输出:
{
  "extracted_data": {
    "表格1": [
      {"列1": "数据1", "列2": "数据2"},
      {"列1": "数据3", "列2": "数据4"}
    ],
    "表格2": [...],
    "summary": "提取了2个表格，共50行数据"
  }
}
```

## 🔧 高级功能

### 批量处理

```bash
# 创建示例任务文件
python batch_scraper.py --create-sample

# 运行批量处理
python batch_scraper.py
```

任务文件格式 (JSON):
```json
{
  "description": "批量爬取任务",
  "tasks": [
    "爬取百度首页的标题: https://www.baidu.com",
    "提取GitHub首页的导航菜单: https://github.com",
    "获取Python官网的版本信息: https://www.python.org"
  ]
}
```

### 自定义配置

```python
from langchain_html_processor import LoaderConfig
from intelligent_web_scraper import IntelligentWebScraper

# 自定义爬虫配置
config = LoaderConfig(
    timeout=120,
    wait_for_js=5.0,
    retries=3,
    user_agent="Custom User Agent"
)

scraper = IntelligentWebScraper("api-key")
scraper.loader_config = config
```

## 🎯 支持的内容类型

### 文本内容
- 标题、描述、正文
- 作者、发布时间
- 标签、分类

### 商品信息
- 产品名称、价格
- 库存状态、规格
- 评分、评论

### 结构化数据
- 表格数据
- 列表信息
- 联系方式

### 媒体内容
- 图片链接
- 视频链接
- 下载链接

## 🔒 安全和最佳实践

### 账号安全
- 不在代码中硬编码密码
- 使用环境变量存储敏感信息
- 定期更换密码

### 爬取规范
- 遵守网站 robots.txt
- 控制请求频率
- 尊重网站使用条款

### 数据保护
- 不爬取个人隐私信息
- 遵守数据保护法规
- 合理使用爬取的数据

## 🛠️ 故障排除

### 常见问题

1. **OpenAI API 错误**
   ```
   错误: API key 无效
   解决: 检查 API key 是否正确设置
   ```

2. **登录失败**
   ```
   错误: 认证爬取失败
   解决: 检查用户名密码，确认网站登录机制
   ```

3. **内容提取失败**
   ```
   错误: 无法提取指定内容
   解决: 调整内容描述，使用更具体的要求
   ```

4. **网络超时**
   ```
   错误: 请求超时
   解决: 增加 timeout 配置，检查网络连接
   ```

### 调试模式

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 启用详细日志
scraper = IntelligentWebScraper("api-key")
```

## 📊 性能优化

### 批量处理优化
- 合理设置请求间隔
- 使用异步处理
- 错误重试机制

### 内容提取优化
- 限制 HTML 内容长度
- 使用缓存机制
- 优化 AI 提示词

### 成本控制
- 监控 OpenAI API 使用量
- 优化提示词长度
- 使用合适的模型

## 🔮 扩展功能

### 自定义提取器
```python
class CustomExtractor(ContentExtractor):
    def extract_content(self, html_content, requirements, url):
        # 自定义提取逻辑
        pass

scraper.content_extractor = CustomExtractor(openai_client)
```

### 插件系统
- 网站特定的处理器
- 自定义登录流程
- 特殊内容格式支持

### 集成其他服务
- 数据库存储
- 消息队列
- 监控告警

## 📞 支持

### 文档和示例
- 查看 `examples/` 目录
- 运行 `interactive_scraper.py` 体验
- 阅读源码注释

### 问题反馈
- 检查日志输出
- 提供完整的错误信息
- 描述复现步骤

## 🎉 总结

这个智能爬虫系统结合了：
- 🧠 **AI 智能** - OpenAI GPT 理解和分析
- 🔧 **技术实力** - 基于 langchain_html_processor
- 🎯 **用户友好** - 自然语言交互
- 🚀 **高效实用** - 批量处理和自动化

让网页爬取变得像聊天一样简单！
